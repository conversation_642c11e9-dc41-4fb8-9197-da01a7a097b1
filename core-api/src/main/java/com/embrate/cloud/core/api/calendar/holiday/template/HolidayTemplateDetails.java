package com.embrate.cloud.core.api.calendar.holiday.template;

import com.embrate.cloud.core.api.calendar.holiday.Holiday;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * <AUTHOR>
 */

public class HolidayTemplateDetails {

    private final HolidayTemplate holidayTemplate;

    private final List<Holiday> holidays;

    public HolidayTemplateDetails(HolidayTemplate holidayTemplate, List<Holiday> holidays) {
        this.holidayTemplate = holidayTemplate;
        this.holidays = holidays;
    }

    public HolidayTemplate getHolidayTemplate() {
        return holidayTemplate;
    }

    public List<Holiday> getHolidays() {
        return holidays;
    }

    @Override
    public String toString() {
        return "HolidayTemplateDetails{" +
                "holidayTemplate=" + holidayTemplate +
                ", holidays=" + holidays +
                '}';
    }
}
