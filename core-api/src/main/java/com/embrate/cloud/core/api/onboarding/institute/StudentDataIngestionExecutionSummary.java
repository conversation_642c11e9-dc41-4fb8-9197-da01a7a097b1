package com.embrate.cloud.core.api.onboarding.institute;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;

/**
 *
 * <AUTHOR>
 *
 */
public class StudentDataIngestionExecutionSummary {

	private final boolean dryRun;

	private final int totalCount;

	private final int successCount;

	private final int failureCount;

	private final List<StudentDataIngestionRecord> executionSuccessStudentDataIngestionRecords;

	private final List<StudentDataIngestionRecord> executionFailureStudentDataIngestionRecords;

	private final List<StudentDataIngestionRecord> admitFailureStudentDataIngestionRecords;

	private final List<StudentDataIngestionRecord> failureStudentDataIngestionRecords;

	public StudentDataIngestionExecutionSummary(boolean dryRun,
			List<StudentDataIngestionRecord> executionSuccessStudentDataIngestionRecords,
			List<StudentDataIngestionRecord> executionFailureStudentDataIngestionRecords,
			List<StudentDataIngestionRecord> admitFailureStudentDataIngestionRecords,
			List<StudentDataIngestionRecord> failureStudentDataIngestionRecords) {
		this.dryRun = dryRun;
		this.executionSuccessStudentDataIngestionRecords = executionSuccessStudentDataIngestionRecords;
		this.executionFailureStudentDataIngestionRecords = executionFailureStudentDataIngestionRecords;
		this.admitFailureStudentDataIngestionRecords = admitFailureStudentDataIngestionRecords;
		this.failureStudentDataIngestionRecords = failureStudentDataIngestionRecords;
		this.successCount = CollectionUtils
				.isEmpty(executionSuccessStudentDataIngestionRecords)
						? 0
						: executionSuccessStudentDataIngestionRecords.size();

		this.failureCount = (CollectionUtils
				.isEmpty(failureStudentDataIngestionRecords)
						? 0
						: failureStudentDataIngestionRecords.size())
				+ (CollectionUtils
						.isEmpty(executionFailureStudentDataIngestionRecords)
								? 0
								: executionFailureStudentDataIngestionRecords
										.size());
		this.totalCount = successCount + failureCount;
	}

	public boolean isDryRun() {
		return dryRun;
	}

	public int getTotalCount() {
		return totalCount;
	}

	public int getSuccessCount() {
		return successCount;
	}

	public int getFailureCount() {
		return failureCount;
	}

	public List<StudentDataIngestionRecord> getExecutionSuccessStudentDataIngestionRecords() {
		return executionSuccessStudentDataIngestionRecords;
	}

	public List<StudentDataIngestionRecord> getExecutionFailureStudentDataIngestionRecords() {
		return executionFailureStudentDataIngestionRecords;
	}

	public List<StudentDataIngestionRecord> getAdmitFailureStudentDataIngestionRecords() {
		return admitFailureStudentDataIngestionRecords;
	}

	public List<StudentDataIngestionRecord> getFailureStudentDataIngestionRecords() {
		return failureStudentDataIngestionRecords;
	}

	@Override
	public String toString() {
		return "StudentDataIngestionExecutionSummary [dryRun=" + dryRun
				+ ", totalCount=" + totalCount + ", successCount="
				+ successCount + ", failureCount=" + failureCount
				+ ", executionSuccessStudentDataIngestionRecords="
				+ executionSuccessStudentDataIngestionRecords
				+ ", executionFailureStudentDataIngestionRecords="
				+ executionFailureStudentDataIngestionRecords
				+ ", admitFailureStudentDataIngestionRecords="
				+ admitFailureStudentDataIngestionRecords
				+ ", failureStudentDataIngestionRecords="
				+ failureStudentDataIngestionRecords + "]";
	}

}
