package com.lernen.cloud.core.api.voicecall;

/**
 * 
 * <AUTHOR>
 *
 */
public class VoiceCallResponse {

	private final boolean success;

	private final String failureReason;

	private final int totalVoiceCallSent;

	private final int totalVoiceCallCredit;

	public VoiceCallResponse(boolean success, String failureReason, int totalVoiceCallSent, int totalVoiceCallCredit) {
		this.success = success;
		this.failureReason = failureReason;
		this.totalVoiceCallSent = totalVoiceCallSent;
		this.totalVoiceCallCredit = totalVoiceCallCredit;
	}

	public boolean isSuccess() {
		return success;
	}

	public String getFailureReason() {
		return failureReason;
	}

	public int getTotalVoiceCallSent() {
		return totalVoiceCallSent;
	}

	public int getTotalVoiceCallCredit() {
		return totalVoiceCallCredit;
	}

	public static VoiceCallResponse successResponse(int totalVoiceCallSent,
													int totalVoiceCallCredit) {
		return new VoiceCallResponse(true, null, totalVoiceCallSent, totalVoiceCallCredit);
	}

	public static VoiceCallResponse failureResponse(String failureReason) {
		return new VoiceCallResponse(false, failureReason, 0, 0);
	}

	@Override
	public String toString() {
		return "VoiceCallResponse{" +
				"success=" + success +
				", failureReason='" + failureReason + '\'' +
				", totalVoiceCallSent=" + totalVoiceCallSent +
				", totalVoiceCallCredit=" + totalVoiceCallCredit +
				'}';
	}
}
