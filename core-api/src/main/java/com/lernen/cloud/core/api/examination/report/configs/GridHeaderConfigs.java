package com.lernen.cloud.core.api.examination.report.configs;

import com.itextpdf.kernel.font.PdfFont;

public class GridHeaderConfigs {

    private final boolean hideDimensionMaxMarks;
    private final boolean hideDimensionsRow;
    private final Float examNamefontSize;
    private final PdfFont examNameFont;

    public GridHeaderConfigs(boolean hideDimensionMaxMarks) {
        this.hideDimensionMaxMarks = hideDimensionMaxMarks;
        this.hideDimensionsRow = false;
        this.examNamefontSize = null;
        this.examNameFont = null;
    }

    public GridHeaderConfigs(boolean hideDimensionMaxMarks, boolean hideDimensionsRow) {
        this.hideDimensionMaxMarks = hideDimensionMaxMarks;
        this.hideDimensionsRow = hideDimensionsRow;
        this.examNamefontSize = null;
        this.examNameFont = null;
    }

    public GridHeaderConfigs(boolean hideDimensionMaxMarks, boolean hideDimensionsRow, float examNamefontSize, PdfFont examNameFont) {
        this.hideDimensionMaxMarks = hideDimensionMaxMarks;
        this.hideDimensionsRow = hideDimensionsRow;
        this.examNamefontSize = examNamefontSize;
        this.examNameFont = examNameFont;
    }

    public GridHeaderConfigs(Float examNamefontSize, PdfFont examNameFont) {
        this.hideDimensionMaxMarks = false;
        this.hideDimensionsRow = false;
        this.examNamefontSize = examNamefontSize;
        this.examNameFont = examNameFont;
    }

    public Float getExamNamefontSize() {
        return examNamefontSize;
    }

    public PdfFont getExamNameFont() {
        return examNameFont;
    }

    public boolean isHideDimensionMaxMarks() {
        return hideDimensionMaxMarks;
    }

    public boolean isHideDimensionsRow() {
        return hideDimensionsRow;
    }

    @java.lang.Override
    public java.lang.String toString() {
        return "GridHeaderConfigs{" +
                "hideDimensionMaxMarks=" + hideDimensionMaxMarks +
                '}';
    }
}
