/**
 * 
 */
package com.lernen.cloud.core.api.staff;

import com.lernen.cloud.core.api.student.StudentStatus;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 *
 */
public enum StaffStatus {
	
	JOINER, ONBOARD, RELIEVED;
	
	public static StaffStatus getStaffStatus(String staffStatus) {
		if (StringUtils.isBlank(staffStatus)) {
			return null;
		}
		for (StaffStatus staffStatusEnum : StaffStatus.values()) {
			if (staffStatusEnum.name().equalsIgnoreCase(staffStatus)) {
				return staffStatusEnum;
			}
		}
		return null;
	}

	public static Set<StaffStatus> getStaffStatusSet(String staffStatusCSV) {
		final Set<StaffStatus> staffStatuses = new LinkedHashSet<>();
		if (StringUtils.isBlank(staffStatusCSV)) {
			return staffStatuses;
		}

		final String[] statusTokens = staffStatusCSV.split(",");

		for (final String status : statusTokens) {
			if (StringUtils.isBlank(status))
			{
				continue;
			}
			staffStatuses.add(getStaffStatus(status.trim()));
		}
		return staffStatuses;
	}
}
