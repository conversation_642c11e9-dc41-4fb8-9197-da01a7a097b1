package com.lernen.cloud.core.api.transport;

import com.embrate.cloud.core.api.transport.tracking.TransportTrackingData;

/**
 * <AUTHOR>
 */

public class TransportTripMetadata {

    private final TransportServiceRouteMetadata transportServiceRouteMetadata;

    private final TransportTrackingData transportTrackingData;

    public TransportTripMetadata(TransportServiceRouteMetadata transportServiceRouteMetadata, TransportTrackingData transportTrackingData) {
        this.transportServiceRouteMetadata = transportServiceRouteMetadata;
        this.transportTrackingData = transportTrackingData;
    }

    public TransportServiceRouteMetadata getTransportServiceRouteMetadata() {
        return transportServiceRouteMetadata;
    }

    public TransportTrackingData getTransportTrackingData() {
        return transportTrackingData;
    }

    @Override
    public String toString() {
        return "TransportTripData{" +
                "transportServiceRouteMetadata=" + transportServiceRouteMetadata +
                ", transportTrackingData=" + transportTrackingData +
                '}';
    }
}
