package com.lernen.cloud.core.api.transport;

import java.util.UUID;

/**
 * 
 * <AUTHOR>
 *
 */
public class StudentTransportPayload {

	private UUID studentId;

	private UUID pickupServiceRouteId;

	private UUID dropServiceRouteId;

	private Integer areaId;

	public UUID getStudentId() {
		return studentId;
	}

	public void setStudentId(UUID studentId) {
		this.studentId = studentId;
	}

	public UUID getPickupServiceRouteId() {
		return pickupServiceRouteId;
	}

	public void setPickupServiceRouteId(UUID pickupServiceRouteId) {
		this.pickupServiceRouteId = pickupServiceRouteId;
	}

	public UUID getDropServiceRouteId() {
		return dropServiceRouteId;
	}

	public void setDropServiceRouteId(UUID dropServiceRouteId) {
		this.dropServiceRouteId = dropServiceRouteId;
	}

	public Integer getAreaId() {
		return areaId;
	}

	public void setAreaId(Integer areaId) {
		this.areaId = areaId;
	}

	@Override
	public String toString() {
		return "BulkTransportAssignmentPayload{" +
				"studentId=" + studentId +
				", pickupServiceRouteId=" + pickupServiceRouteId +
				", dropServiceRouteId=" + dropServiceRouteId +
				", areaId=" + areaId +
				'}';
	}
}
