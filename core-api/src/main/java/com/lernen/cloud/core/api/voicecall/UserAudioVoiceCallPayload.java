/**
 * 
 */
package com.lernen.cloud.core.api.voicecall;

import com.embrate.cloud.core.api.service.communication.UserCommunicationServicePayload;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 *
 */
public class UserAudioVoiceCallPayload extends UserCommunicationServicePayload {

	public static final String EXTERNAL_VOICE_ID_KEY = "voiceId";
	public static final String INTERNAL_VOICE_TEMPLATE_ID_KEY = "templateId";

	private final AudioVoiceCallData audioVoiceCallData;

	public UserAudioVoiceCallPayload(UUID userId, List<String> mobileNumbers, AudioVoiceCallData audioVoiceCallData, Map<String, Object> metaData, Integer creditUsed, String dltTemplateId) {
		super(userId, mobileNumbers, metaData, creditUsed, dltTemplateId);
		this.audioVoiceCallData = audioVoiceCallData;
	}

	public AudioVoiceCallData getAudioVoiceCallData() {
		return audioVoiceCallData;
	}

	@Override
	public String getTitle() {
		return null;
	}

	@Override
	public String getMessagePayload() {
		return audioVoiceCallData.getVoiceId();
	}

	@Override
	public String getDisplayMessage() {
		return audioVoiceCallData.getTemplateName();
	}

	@Override
	public Float getMessageDuration() {
		return audioVoiceCallData.getAudioFileProperties().getDuration();
	}

	@Override
	public String toString() {
		return "UserAudioVoiceCallPayload{" +
				"audioVoiceCallData=" + audioVoiceCallData +
				'}';
	}
}
