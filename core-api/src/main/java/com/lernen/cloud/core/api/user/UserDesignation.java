package com.lernen.cloud.core.api.user;

import org.apache.commons.lang3.StringUtils;

/**
 * 
 * Defines the various posts in school
 * 
 * <AUTHOR>
 *
 */
public enum UserDesignation {

	CLASS_TEACHER,
	HOD,
	PRINCIPAL,
	VICE_PRINCIPAL,
	T<PERSON>CHER,
	LIBRARIAN,
	ST<PERSON><PERSON>NT,
	STAFF,
	OTHER;
	
	public static UserDesignation getUserDesignation(String userDesignation){
		if(StringUtils.isBlank(userDesignation)){
			return null;
		}
		for(UserDesignation userDesignationEnum : UserDesignation.values()){
			if(userDesignationEnum.name().equalsIgnoreCase(userDesignation)){
				return userDesignationEnum;
			}
		}
		return null;
	}
}
