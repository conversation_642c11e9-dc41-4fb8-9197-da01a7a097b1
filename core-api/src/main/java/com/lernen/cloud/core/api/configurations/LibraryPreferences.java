package com.lernen.cloud.core.api.configurations;

public class LibraryPreferences {
    
    public static final String ENABLE_ACCESSION_NUMBER = "enable_accession_number";
	public static final String ACCESSION_NUMBER_COUNTER = "accession_number_counter";

    private boolean enableAccessionNumber;
	private boolean accessionNumberCounter;


    public LibraryPreferences() {
    }

    public static String getConfigType() {
		return "library_preferences";
	}
    
    public boolean isEnableAccessionNumber() {
        return enableAccessionNumber;
    }
    public void setEnableAccessionNumber(boolean enableAccessionNumber) {
        this.enableAccessionNumber = enableAccessionNumber;
    }
    public boolean isAccessionNumberCounter() {
        return accessionNumberCounter;
    }
    public void setAccessionNumberCounter(boolean accessionNumberCounter) {
        this.accessionNumberCounter = accessionNumberCounter;
    }
    @Override
    public String toString() {
        return "LibraryPrefrences [enableAccessionNumber=" + enableAccessionNumber + ", accessionNumberCounter="
                + accessionNumberCounter + "]";
    }

    
}
