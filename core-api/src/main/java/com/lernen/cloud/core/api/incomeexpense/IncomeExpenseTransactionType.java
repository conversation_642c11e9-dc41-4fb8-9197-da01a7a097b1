/**
 * 
 */
package com.lernen.cloud.core.api.incomeexpense;

import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 *
 */
public enum IncomeExpenseTransactionType {
	
INCOME("Income"), EXPENSE("Expense");

	private String displayName;

	IncomeExpenseTransactionType(String displayName) {
		this.displayName = displayName;
	}
	
	public static IncomeExpenseTransactionType getTransactionType(String transactionType) {
		if (StringUtils.isBlank(transactionType)) {
			return null;
		}
		for (IncomeExpenseTransactionType transactionTypeEnum : IncomeExpenseTransactionType.values()) {
			if (transactionTypeEnum.name().equalsIgnoreCase(transactionType)) {
				return transactionTypeEnum;
			}
		}
		return null;
	}

	public String getDisplayName() {
        return displayName;
    }

}
