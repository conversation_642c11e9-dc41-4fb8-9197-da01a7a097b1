package com.lernen.cloud.core.api.attendance.staff;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public enum StaffAttendanceStatus {

    PRESENT("Present", "P"),
    HALF_DAY("Half-Day", "HD"),
    LEAVE("Leave", "L");

    private String displayName;

    private String abbreviation;

    StaffAttendanceStatus(String displayName, String abbreviation) {
        this.displayName = displayName;
        this.abbreviation = abbreviation;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getAbbreviation() {
        return abbreviation;
    }

    public void setAbbreviation(String abbreviation) {
        this.abbreviation = abbreviation;
    }

    public static StaffAttendanceStatus getStaffAttendanceStatus(String staffAttendanceStatus) {
        if (StringUtils.isBlank(staffAttendanceStatus)) {
            return null;
        }
        for (StaffAttendanceStatus staffAttendanceStatusEnum : StaffAttendanceStatus.values()) {
            if (staffAttendanceStatusEnum.name().equalsIgnoreCase(staffAttendanceStatus)) {
                return staffAttendanceStatusEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return "StaffAttendanceStatus{" +
                "displayName='" + displayName + '\'' +
                ", abbreviation='" + abbreviation + '\'' +
                '}';
    }
}
