package com.embrate.cloud.dao.tier.dashboard.fees;

import com.embrate.cloud.core.api.dashboards.fees.FeeCollectionByPaymentMode;
import com.embrate.cloud.core.api.dashboards.fees.FeeCollectionByPaymentModeFeeHead;
import com.embrate.cloud.dao.tier.dashboard.fees.mapper.FeeCollectionByPaymentModeFeeHeadRowMapper;
import com.embrate.cloud.dao.tier.dashboard.fees.mapper.FeeCollectionByPaymentModeRowMapper;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * DAO class for student session summary queries
 *
 * <AUTHOR>
 */
public class FeesDashboardDao {

	private static final Logger logger = LogManager.getLogger(FeesDashboardDao.class);

	private static final FeeCollectionByPaymentModeRowMapper FEE_COLLECTION_ROW_MAPPER = new FeeCollectionByPaymentModeRowMapper();
	private static final FeeCollectionByPaymentModeFeeHeadRowMapper FEE_COLLECTION_BY_PAYMENT_MODE_FEE_HEAD_ROW_MAPPER = new FeeCollectionByPaymentModeFeeHeadRowMapper();


	private static final String GET_FEES_COLLECTED_BY_TRANSACTION_MODE = "select institute_id, transaction_mode, sum(t_paid_amount) from fee_payment_transactions where " +
			"institute_id in (%s) and transaction_date between ? and ? group by 1, 2";

	private static final String GET_FEES_COLLECTED_BY_TRANSACTION_MODE_FEE_HEAD = "select fee_payment_transactions.institute_id, transaction_mode, fee_head, " +
			" sum(paid_amount) t_amount from fee_payment_transactions join " +
			" fee_payment_transaction_amounts on fee_payment_transactions.transaction_id = fee_payment_transaction_amounts.transaction_id " +
			" join fee_head_configuration on fee_payment_transaction_amounts.fee_head_id = fee_head_configuration.fee_head_id " +
			" where fee_payment_transactions.institute_id in (%s) and transaction_date between ? and ? group by 1, 2, 3";

	private final JdbcTemplate jdbcTemplate;

	public FeesDashboardDao(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
	}

	/**
	 * Helper method to create parameterized IN clause placeholders
	 *
	 * @param size Number of parameters
	 * @return String with comma-separated question marks (e.g., "?,?,?")
	 */
	private String createInClausePlaceholders(int size) {
		return String.join(",", Collections.nCopies(size, "?"));
	}

	private Timestamp convertToSqlDate(int unixTimestamp) {
		return new Timestamp(unixTimestamp * 1000L);
	}

	private Object[] createParameterArray(List<Integer> instituteIds, Timestamp startDate, Timestamp endDate) {
		List<Object> params = new ArrayList<>();
		params.addAll(instituteIds);
		if (startDate != null) {
			params.add(startDate);
		}
		if (endDate != null) {
			params.add(endDate);
		}
		return params.toArray();
	}

	public List<FeeCollectionByPaymentMode> getFeesCollectedByTransactionMode(List<Integer> instituteIds,
																			  int startDate,
																			  int endDate) {
		if (CollectionUtils.isEmpty(instituteIds)) {
			logger.warn("Institute IDs are empty");
			return null;
		}

		if (startDate <= 0 || endDate <= 0 || startDate > endDate) {
			logger.warn("Start date {} or end date {} is invalid, instituteIds {}", startDate, endDate, instituteIds);
			return null;
		}

		try {
			// Create parameterized query with placeholders
			String institutePlaceholders = createInClausePlaceholders(instituteIds.size());
			String query = String.format(GET_FEES_COLLECTED_BY_TRANSACTION_MODE, institutePlaceholders);

			// Convert dates and create parameter array
			Timestamp sqlStartDate = convertToSqlDate(startDate);
			Timestamp sqlEndDate = convertToSqlDate(endDate);
			Object[] params = createParameterArray(instituteIds, sqlStartDate, sqlEndDate);

			logger.debug("Executing parameterized query: {} with {} parameters", query, params.length);
			return jdbcTemplate.query(query, params, FEE_COLLECTION_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting fees collected by transaction mode for institutes {} between {} and {}",
					instituteIds, startDate, endDate, e);
		}
		return null;
	}

	public List<FeeCollectionByPaymentModeFeeHead> getFeesCollectedByTransactionModeFeeHead(List<Integer> instituteIds,
																							int startDate,
																							int endDate) {
		if (CollectionUtils.isEmpty(instituteIds)) {
			logger.warn("Institute IDs are empty");
			return null;
		}

		if (startDate <= 0 || endDate <= 0 || startDate > endDate) {
			logger.warn("Start date {} or end date {} is invalid, instituteIds {}", startDate, endDate, instituteIds);
			return null;
		}

		try {
			// Create parameterized query with placeholders
			String institutePlaceholders = createInClausePlaceholders(instituteIds.size());
			String query = String.format(GET_FEES_COLLECTED_BY_TRANSACTION_MODE_FEE_HEAD, institutePlaceholders);

			// Convert dates and create parameter array
			Timestamp sqlStartDate = convertToSqlDate(startDate);
			Timestamp sqlEndDate = convertToSqlDate(endDate);
			Object[] params = createParameterArray(instituteIds, sqlStartDate, sqlEndDate);

			logger.info("Executing parameterized query: {} with {} parameters", query, params);
			return jdbcTemplate.query(query, params, FEE_COLLECTION_BY_PAYMENT_MODE_FEE_HEAD_ROW_MAPPER);
		} catch (final Exception e) {
			logger.error("Error while getting fees collected by transaction mode for institutes {} between {} and {}",
					instituteIds, startDate, endDate, e);
		}
		return null;
	}

}
