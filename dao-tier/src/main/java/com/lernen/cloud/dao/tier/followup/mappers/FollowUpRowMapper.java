package com.lernen.cloud.dao.tier.followup.mappers;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.UUID;

import com.lernen.cloud.core.api.followup.FollowUpEntity;
import com.lernen.cloud.core.api.followup.FollowUpMode;
import com.lernen.cloud.core.api.followup.FollowUpStatus;
import com.lernen.cloud.core.api.followup.FollowUpType;
import com.lernen.cloud.core.api.followup.FollowUpPayload;
import org.springframework.jdbc.core.RowMapper;

public class FollowUpRowMapper implements RowMapper<FollowUpPayload>{

    private static final String FOLLOW_UP_ID = "follow_up_id";
    private static final String ENTITY_ID = "entity_id";
    private static final String ENTITY_NAME = "entity_name";
    private static final String FOLLOW_UP_DATE = "follow_up_date";
    private static final String FOLLOW_UP_MODE = "mode";
    private static final String CONTACT_PERSON = "contact_person";
    private static final String CONVERSATION = "conversation";
    private static final String NEXT_FOLLOW_UP_DATE = "next_follow_up_date";
    private static final String AMOUNT = "amount";
    private static final String FOLLOW_UP_TYPE = "follow_up_type";
    private static final String FOLLOW_UP_STATUS = "follow_up_status";
    private static final String ADDED_BY = "added_by";

    private static final String ADDED_AT = "added_at";
    private static final String UPDATED_BY = "updated_by";

    @Override
    public FollowUpPayload mapRow(ResultSet rs, int rowNum) throws SQLException {
        if(rs.getString(FOLLOW_UP_ID)== null){
            return null;
        }
        final Timestamp followUpDate = rs.getTimestamp(FOLLOW_UP_DATE);
        final Integer followUpDateTime = followUpDate == null ? null
                : (int) (followUpDate.getTime() / 1000l);

        final Timestamp nextFollowUpDate = rs.getTimestamp(NEXT_FOLLOW_UP_DATE);
        final Integer nextFollowUpDateTime = nextFollowUpDate == null ? null
                : (int) (nextFollowUpDate .getTime() / 1000l);

        final Timestamp addedAtDate = rs.getTimestamp(ADDED_AT);
        final Integer addedAtDateTime = addedAtDate == null ? null
                : (int) (addedAtDate.getTime() / 1000l);

        String updatedByString = rs.getString(UPDATED_BY);
        UUID updatedBy = (updatedByString != null) ? UUID.fromString(updatedByString) : null;

        return new FollowUpPayload(UUID.fromString(rs.getString(FOLLOW_UP_ID)),UUID.fromString(rs.getString(ENTITY_ID)), FollowUpEntity.valueOf(rs.getString(ENTITY_NAME)),
                followUpDateTime, FollowUpMode.valueOf(rs.getString(FOLLOW_UP_MODE)),rs.getString(CONTACT_PERSON),rs.getString(CONVERSATION),nextFollowUpDateTime,rs.getDouble(AMOUNT), FollowUpType.valueOf(rs.getString(FOLLOW_UP_TYPE)), FollowUpStatus.valueOf(rs.getString(FOLLOW_UP_STATUS)),
                UUID.fromString(rs.getString(ADDED_BY)), addedAtDateTime, updatedBy);
    }
}
