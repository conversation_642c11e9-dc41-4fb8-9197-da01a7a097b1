package com.lernen.cloud.dao.tier.institute.mappers;

import com.lernen.cloud.core.api.institute.AcademicSession;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.util.CollectionUtils;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.time.Month;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AcademicSessionRowMapper implements RowMapper<AcademicSession> {

    private static final String INSTITUTE_ID = "institute_id";
    private static final String ACADEMIC_SESSION_ID = "academic_session_id";
    private static final String START_YEAR = "academic_session.start_year";
    private static final String END_YEAR = "academic_session.end_year";
    private static final String START_MONTH = "academic_session.start_month";
    private static final String END_MONTH = "academic_session.end_month";
    private static final String DISPLAY_NAME = "academic_session.display_name";

    private static final String PAYROLL_START_YEAR = "academic_session.payroll_start_year";
    private static final String PAYROLL_END_YEAR = "academic_session.payroll_end_year";
    private static final String PAYROLL_START_MONTH = "academic_session.payroll_start_month";
    private static final String PAYROLL_END_MONTH = "academic_session.payroll_end_month";
    private static final String PAYROLL_DISPLAY_NAME = "academic_session.payroll_display_name";

    private static final String ADDED_TIMESTAMP = "academic_session.added_timestamp";
    private static final String UPDATED_TIMESTAMP = "academic_session.updated_timestamp";

    @Override
    public AcademicSession mapRow(ResultSet rs, int rowNum) throws SQLException {
        final Timestamp addedTimestamp = rs.getTimestamp(ADDED_TIMESTAMP);
        final int addedTimestampTime = addedTimestamp == null ? 0
                : (int) (addedTimestamp.getTime() / 1000l);

        final Timestamp updatedTimestamp = rs.getTimestamp(UPDATED_TIMESTAMP);
        final int updatedTimestampTime = updatedTimestamp == null ? 0
                : (int) (updatedTimestamp.getTime() / 1000l);

        return new AcademicSession(rs.getInt(INSTITUTE_ID), rs.getInt(ACADEMIC_SESSION_ID), rs.getInt(START_YEAR),
                rs.getInt(END_YEAR), Month.of(rs.getInt(START_MONTH)), Month.of(rs.getInt(END_MONTH)),
                rs.getString(DISPLAY_NAME),
                rs.getInt(PAYROLL_START_YEAR),
                rs.getInt(PAYROLL_END_YEAR), Month.of(rs.getInt(PAYROLL_START_MONTH)), Month.of(rs.getInt(PAYROLL_END_MONTH)),
                rs.getString(PAYROLL_DISPLAY_NAME),
                addedTimestampTime, updatedTimestampTime);
    }

    /**
     * @param academicSessionList
     * @return multiple institute id
     * Institute Id, List<AcademicSession>
     */
    public static Map<Integer, List<AcademicSession>> getInstituteAcademicSessionMap(List<AcademicSession> academicSessionList) {
        if (CollectionUtils.isEmpty(academicSessionList)) {
            return null;
        }
        Map<Integer, List<AcademicSession>> instituteAcademicSessionMap = new HashMap<>();
        for (AcademicSession academicSession : academicSessionList) {
            int instituteId = academicSession.getInstituteId();
            if (instituteAcademicSessionMap.containsKey(instituteId)) {
                instituteAcademicSessionMap.get(instituteId).add(academicSession);
            } else {
                List<AcademicSession> academicSessionList1 = new ArrayList<>();
                academicSessionList1.add(academicSession);
                instituteAcademicSessionMap.put(instituteId, academicSessionList1);
            }
        }
        return CollectionUtils.isEmpty(instituteAcademicSessionMap) ? null : instituteAcademicSessionMap;
    }

}
