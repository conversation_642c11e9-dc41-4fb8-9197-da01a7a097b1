
package com.lernen.cloud.core.lib.examination;

import com.embrate.cloud.core.api.examination.config.ExamMarksDisplayType;
import com.embrate.cloud.core.api.institute.StandardMetadata;
import com.embrate.cloud.core.api.examination.config.StandardMetadataPayloadReadable;
import com.embrate.cloud.core.api.examination.management.GradeUpdatePayload;
import com.embrate.cloud.core.api.examination.management.GradeRenamePayload;
import com.embrate.cloud.core.api.examination.management.StandardGradeDetails;
import com.embrate.cloud.core.api.examination.utility.ExaminationGradesPayloadReadable;
import com.embrate.cloud.core.api.examination.utility.StandardExaminationGrades;
import com.embrate.cloud.core.lib.examination.marks.computation.IMarksComputator;
import com.embrate.cloud.core.lib.examination.marks.computation.MarksComputationFactory;
import com.embrate.cloud.core.lib.timetable.TimetableManager;
import com.embrate.cloud.core.utils.institute.StandardUtils;
import com.lernen.cloud.core.api.configurations.ExaminationPreferences;
import com.lernen.cloud.core.api.course.Course;
import com.lernen.cloud.core.api.course.CourseStudents;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.examination.*;
import com.lernen.cloud.core.api.examination.report.greensheet.GreenSheetClassStructure;
import com.lernen.cloud.core.api.examination.report.greensheet.GreenSheetExamDimensionMapData;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.EmbrateRunTimeException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.*;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.permissions.UserPermissions;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.student.StudentSortingParameters;
import com.lernen.cloud.core.api.student.StudentStatus;
import com.lernen.cloud.core.api.user.DataUpdationAction;
import com.embrate.cloud.core.lib.courses.CourseManager;
import com.lernen.cloud.core.lib.configs.UserPreferenceSettings;
import com.lernen.cloud.core.lib.google.auth.GoogleRecaptchaManager;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.Pair;
import com.lernen.cloud.core.utils.permissions.EPermissionUtils;
import com.lernen.cloud.core.utils.student.StudentSorter;
import com.lernen.cloud.dao.tier.examination.ExaminationDao;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.Map.Entry;


/**
 *
 * <AUTHOR>
 *
 */
public class ExaminationManager {
	private static final Logger logger = LogManager.getLogger(ExaminationManager.class);
	private static final Double DEFAULT_MAX_MARKS = 100d;
	private static final String EXAM_STRUCTURE = "Exam Structure";
	private final ExaminationDao examinationDao;
	private final InstituteManager instituteManager;
	private final CourseManager courseManager;
	private final UserPermissionManager userPermissionManager;
	private final GoogleRecaptchaManager googleRecaptchaManager;
	private final StudentManager studentManager;
	private final MarksComputationFactory marksComputationFactory;
	private final TimetableManager timetableManager;
	private final UserPreferenceSettings userPreferenceSettings;

	public ExaminationManager(ExaminationDao examinationDao, InstituteManager instituteManager,
			CourseManager courseManager, UserPermissionManager userPermissionManager,
			GoogleRecaptchaManager googleRecaptchaManager, StudentManager studentManager,
			MarksComputationFactory marksComputationFactory, TimetableManager timetableManager,
							  UserPreferenceSettings userPreferenceSettings) {
		this.examinationDao = examinationDao;
		this.instituteManager = instituteManager;
		this.courseManager = courseManager;
		this.userPermissionManager = userPermissionManager;
		this.googleRecaptchaManager = googleRecaptchaManager;
		this.studentManager = studentManager;
		this.marksComputationFactory = marksComputationFactory;
		this.timetableManager = timetableManager;
		this.userPreferenceSettings = userPreferenceSettings;
	}

	public List<ExamDimension> getExamDimensions(int instituteId) {
		return examinationDao.getExamDimensions(instituteId);
	}

	public boolean createExamDimension(int instituteId, ExamDimension dimensionPayload) {
		if (instituteId <= 0 || dimensionPayload == null || StringUtils.isBlank(dimensionPayload.getDimensionName())
				|| dimensionPayload.getDimensionType() == null || dimensionPayload.getExamEvaluationType() == null) {
			logger.error("Invalid dimension payload");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Invalid dimension creation payload"));
		}
		return examinationDao.createExamDimension(instituteId, dimensionPayload);
	}

	/**
	 * Use this method only to get the exam details. As filtering is required
	 * for dimensions based on the configurations
	 *
	 * @param examId
	 * @param instituteId
	 * @return
	 */
	public ExamDetails getExamDetails(UUID examId, int instituteId) {
		final ExamDetails examDetails = examinationDao.getExamDetails(instituteId, examId);
		return getConfigurationBasedExamDetails(examDetails);
	}

	public ExamMetaData getSystemExamMetadata(int instituteId, int academicSessionId, UUID standardId) {
		if (instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute id"));
		}
		if (academicSessionId <= 0) {
			logger.error("Invalid academic session id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id"));
		}
		if (standardId == null) {
			logger.error("Invalid standard id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid standard id"));
		}
		return examinationDao.getSystemExamMetadata(instituteId, academicSessionId, standardId);
	}

	private ExamDetails getConfigurationBasedExamDetails(ExamDetails examDetails) {
		final StandardMetadata standardMetaData = examDetails.getStandardsMetaData();

		final Map<CourseType, List<ExamDimensionValues>> examDimensionValuesMap = examDetails.getExamDimensionValues();
		for (final Entry<CourseType, List<ExamDimensionValues>> entry : examDimensionValuesMap.entrySet()) {
			filterDimensions(entry.getKey(), entry.getValue(), standardMetaData);
		}

		final List<ExamCourse> examCourses = examDetails.getExamCourses();
		for (final ExamCourse examCourse : examCourses) {
			filterDimensions(examCourse.getCourse().getCourseType(), examCourse.getExamDimensionValues(),
					standardMetaData);
		}

		return new ExamDetails(examDetails.getAcademicSession(), examDetails.getStandard(),
				examDetails.getExamMetaData(), examDetails.getParentExamMetaData(), examDimensionValuesMap, examCourses,
				examDetails.getChildExams(), examDetails.getStandardsMetaData());
	}

	private <T extends ExamDimensionValues> void filterDimensions(CourseType courseType, List<T> examDimensionValues,
			StandardMetadata standardMetaData) {
		final boolean isCoscholasticGradingEnabled = standardMetaData == null ? false
				: standardMetaData.isCoScholasticGradingEnabled();

		final boolean isScholasticGradingEnabled = standardMetaData == null ? false
				: standardMetaData.isScholasticGradingEnabled();

		final Iterator<T> examDimensionValuesIterator = examDimensionValues.iterator();
		while (examDimensionValuesIterator.hasNext()) {
			final ExamDimension examDimension = examDimensionValuesIterator.next().getExamDimension();
			if (courseType == CourseType.SCHOLASTIC) {
				if (isScholasticGradingEnabled && examDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {
					examDimensionValuesIterator.remove();
				} else if (!isScholasticGradingEnabled
						&& examDimension.getExamEvaluationType() == ExamEvaluationType.GRADE) {
					examDimensionValuesIterator.remove();
				}
			}

			else if (courseType == CourseType.COSCHOLASTIC) {
				if (isCoscholasticGradingEnabled
						&& examDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {
					examDimensionValuesIterator.remove();
				} else if (!isCoscholasticGradingEnabled
						&& examDimension.getExamEvaluationType() == ExamEvaluationType.GRADE) {
					examDimensionValuesIterator.remove();
				}
			}
		}
	}

	/**
	 * Filters the dimensions based on configurations.
	 *
	 * @param instituteId
	 * @param academicSessionId
	 * @param standardId
	 * @return
	 */
	public List<ExamDetails> getExamDetails(int instituteId, int academicSessionId, UUID standardId) {
		final List<ExamDetails> configurationBasedExamDetailsList = new ArrayList<>();
		final List<ExamDetails> examDetailsList = examinationDao.getExamDetails(instituteId, academicSessionId,
				standardId);
		for (final ExamDetails examDetails : examDetailsList) {
			configurationBasedExamDetailsList.add(getConfigurationBasedExamDetails(examDetails));
		}
		return configurationBasedExamDetailsList;
	}

	/**
	 * Filters the dimensions based on configurations.
	 *
	 * @param instituteId
	 * @param examId
	 * @return
	 */
	public List<ExamDetails> getAllExamDetailsInStandardByExamId(int instituteId, UUID examId) {
		final List<ExamDetails> configurationBasedExamDetailsList = new ArrayList<>();
		final List<ExamDetails> examDetailsList = examinationDao.getAllExamDetailsInStandardByExamId(instituteId,
				examId);

		for (final ExamDetails examDetails : examDetailsList) {
			configurationBasedExamDetailsList.add(getConfigurationBasedExamDetails(examDetails));
		}
		return configurationBasedExamDetailsList;
	}

	public UUID createExam(ExamCreationPayload examCreationPayload, int instituteId) {
		return createExam(examCreationPayload, instituteId, null, false);
	}

	public UUID createExam(ExamCreationPayload examCreationPayload, int instituteId, UUID userId, boolean checkAccess) {
		if (instituteId <= 0 || examCreationPayload == null || examCreationPayload.getAcademicSessionId() <= 0
				|| examCreationPayload.getStandardId() == null
				|| StringUtils.isBlank(examCreationPayload.getExamName())) {
			logger.error("Invalid exam creation payload");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Invalid exam creation payload"));
		}

		if(checkAccess) {
			if (userId == null) {
				logger.error("Invalid user id");
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id"));
			}
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ADD_EXAM);
		}


		if (!instituteManager.validStandard(examCreationPayload.getStandardId(), instituteId,
				examCreationPayload.getAcademicSessionId())) {
			logger.error("Invalid standard {} for institute {} in exam creation payload",
					examCreationPayload.getStandardId(), instituteId);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "Standard does not belong to institute."));
		}

		// Default is sum computation operation
		if(examCreationPayload.getOperation() == null){
			examCreationPayload.setOperation(MarksComputationOperation.SUM);
		}

		if(examCreationPayload.getScholasticExamMarksDisplayType() == null){
			examCreationPayload.setScholasticExamMarksDisplayType(ExamMarksDisplayType.DEFAULT);
		}

		if(examCreationPayload.getCoScholasticExamMarksDisplayType() == null){
			examCreationPayload.setCoScholasticExamMarksDisplayType(ExamMarksDisplayType.DEFAULT);
		}
		/**
		 * Creating course types in exam if not coming in request
		 */
		if (examCreationPayload.getCourseTypeDimensions() == null) {
			examCreationPayload.setCourseTypeDimensions(new HashMap<>());
		}

		if (!examCreationPayload.getCourseTypeDimensions().containsKey(CourseType.SCHOLASTIC)) {
			examCreationPayload.getCourseTypeDimensions().put(CourseType.SCHOLASTIC, new ArrayList<>());
		}
		if (!examCreationPayload.getCourseTypeDimensions().containsKey(CourseType.COSCHOLASTIC)) {
			examCreationPayload.getCourseTypeDimensions().put(CourseType.COSCHOLASTIC, new ArrayList<>());
		}

		updateDefaultDimension(instituteId, examCreationPayload.getAcademicSessionId(),
				examCreationPayload.getStandardId(), examCreationPayload.getCourseTypeDimensions());
		return examinationDao.createExam(examCreationPayload);

	}

	private void updateDefaultDimension(int instituteId, int academicSessionId, UUID standardId,
			Map<CourseType, List<ExamDimensionValuesPayload>> courseTypeExamDimensionValues) {

		final UpdateDimensionAttributes updateDimensionAttributes = getUpdateDimensionAttributes(academicSessionId,
				standardId, instituteId);

		for (final Entry<CourseType, List<ExamDimensionValuesPayload>> courseTypeDimensionEntry : courseTypeExamDimensionValues
				.entrySet()) {
			final CourseType courseType = courseTypeDimensionEntry.getKey();

			addDefaultDimensions(updateDimensionAttributes, courseType, courseTypeDimensionEntry.getValue());

		}
	}

	private void updateDefaultDimension(int instituteId, int academicSessionId, UUID standardId,
			Map<UUID, List<ExamDimensionValuesPayload>> courseExamDimensionValues, Map<UUID, Course> courseIdMap) {

		final UpdateDimensionAttributes updateDimensionAttributes = getUpdateDimensionAttributes(academicSessionId,
				standardId, instituteId);

		for (final Entry<UUID, List<ExamDimensionValuesPayload>> courseDimensionEntry : courseExamDimensionValues
				.entrySet()) {
			final UUID courseId = courseDimensionEntry.getKey();
			if (!courseIdMap.containsKey(courseId)) {
				throw new EmbrateRunTimeException("Course does not exists");
			}
			final Course course = courseIdMap.get(courseId);
			final CourseType courseType = course.getCourseType();

			addDefaultDimensions(updateDimensionAttributes, courseType, courseDimensionEntry.getValue());

		}
	}

	private UpdateDimensionAttributes getUpdateDimensionAttributes(int academicSessionId, UUID standardId,
			int instituteId) {
		final StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId, academicSessionId,
				standardId);

		final boolean isCoScholasticGradingEnabled = standardMetaData == null ? false
				: standardMetaData.isCoScholasticGradingEnabled();

		final boolean isScholasticGradingEnabled = standardMetaData == null ? false
				: standardMetaData.isScholasticGradingEnabled();
		/**
		 * Total dimension is by default added to an exam. Currently it is
		 * assumed that scholastic courses will have NUMBER dimension where as
		 * co-scholastic will have GRADE or number depending on flag enabled at
		 * class level.
		 */
		final List<ExamDimension> examDimensions = getExamDimensions(instituteId);
		ExamDimension totalNumberExamDimension = null;
		ExamDimension totalGradeExamDimension = null;
		final Map<Integer, ExamDimension> examDimensionMap = new HashMap<>();
		for (final ExamDimension examDimension : examDimensions) {
			examDimensionMap.put(examDimension.getDimensionId(), examDimension);

			if (examDimension.isTotal() && examDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {
				totalNumberExamDimension = examDimension;
			}
			if (examDimension.isTotal() && examDimension.getExamEvaluationType() == ExamEvaluationType.GRADE) {
				totalGradeExamDimension = examDimension;
			}
		}

		if (totalNumberExamDimension == null || totalGradeExamDimension == null) {
			logger.error(
					"Total dimensions for number and grade are not present for institute {}.Skipping exam creation",
					instituteId);
			throw new EmbrateRunTimeException("Total dimensions for number and grade are not present for institute");
		}

		return new UpdateDimensionAttributes(isCoScholasticGradingEnabled, isScholasticGradingEnabled,
				totalNumberExamDimension, totalGradeExamDimension, examDimensionMap);
	}

	private void addDefaultDimensions(UpdateDimensionAttributes updateDimensionAttributes, final CourseType courseType,
			final List<ExamDimensionValuesPayload> examDimensionValuesPayloads) {

		final boolean isCoScholasticGradingEnabled = updateDimensionAttributes.isCoScholasticGradingEnabled();
		final boolean isScholasticGradingEnabled = updateDimensionAttributes.isScholasticGradingEnabled();
		final ExamDimension totalNumberExamDimension = updateDimensionAttributes.getTotalNumberExamDimension();
		final ExamDimension totalGradeExamDimension = updateDimensionAttributes.getTotalGradeExamDimension();

		final Map<Integer, ExamDimension> examDimensionMap = updateDimensionAttributes.getExamDimensionMap();

		final Iterator<ExamDimensionValuesPayload> iterator = examDimensionValuesPayloads.iterator();

		while (iterator.hasNext()) {
			final ExamDimensionValuesPayload examDimensionValuesPayload = iterator.next();
			if (!examDimensionMap.containsKey(examDimensionValuesPayload.getDimensionId())) {
				logger.error("Invalid dimension {} provided in payload. Exitting...",
						examDimensionValuesPayload.getDimensionId());
				throw new EmbrateRunTimeException("Invalid dimension provided in payload");
			}
			/**
			 * Remove total dimension coming in payload as those are system
			 * controlled dimensions
			 */

			if (examDimensionValuesPayload.getDimensionId() == totalGradeExamDimension.getDimensionId()
					|| examDimensionValuesPayload.getDimensionId() == totalNumberExamDimension.getDimensionId()) {
				iterator.remove();
				continue;
			}

			final ExamDimension examDimension = examDimensionMap.get(examDimensionValuesPayload.getDimensionId());

			/**
			 * SCHOLASTIC is only supporting marks. Filter grade dimensions
			 */
			if (courseType == CourseType.SCHOLASTIC) {

				if (isScholasticGradingEnabled) {
					if (examDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {
						iterator.remove();
					}
				} else if (examDimension.getExamEvaluationType() == ExamEvaluationType.GRADE) {
					iterator.remove();
				}

			}
			/**
			 * COSCHOLASTIC filter based on class level enabled flag for grading
			 */
			else if (courseType == CourseType.COSCHOLASTIC) {
				if (isCoScholasticGradingEnabled) {
					if (examDimension.getExamEvaluationType() == ExamEvaluationType.NUMBER) {
						iterator.remove();
					}
				} else if (examDimension.getExamEvaluationType() == ExamEvaluationType.GRADE) {
					iterator.remove();
				}
			}
		}

		final ExamDimensionValuesPayload marksExamDimensionValuesPayload = new ExamDimensionValuesPayload();
		final ExamDimensionValuesPayload gradeExamDimensionValuesPayload = new ExamDimensionValuesPayload();

		marksExamDimensionValuesPayload.setDimensionId(totalNumberExamDimension.getDimensionId());
		gradeExamDimensionValuesPayload.setDimensionId(totalGradeExamDimension.getDimensionId());

		/**
		 * Create both grade and marks total dimensions for both type of course
		 * types
		 */
		examDimensionValuesPayloads.add(marksExamDimensionValuesPayload);
		examDimensionValuesPayloads.add(gradeExamDimensionValuesPayload);
	}

	public boolean deleteExam(int instituteId, UUID examId, UUID userId) {
		if (instituteId <= 0 || examId == null) {
			logger.error("Invalid exam details");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Invalid exam details"));
		}
		if (userId == null) {
			logger.error("Invalid user id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id"));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.DELETE_EXAM);
		return examinationDao.deleteExam(instituteId, examId);
	}

	// Must be used for internal usability and within transaction
	public boolean deleteExamNonAtomic(int instituteId, UUID examId) {
		return examinationDao.deleteExamNonAtomic(instituteId, examId);
	}

	public boolean addCoursesInExam(int instituteId, UUID examId, CourseType courseType,
									List<ExamCoursesPayload> examCoursesPayloads, Boolean update) {
		return addCoursesInExam(instituteId, examId, courseType, examCoursesPayloads, update, null, false);
	}

	public boolean addCoursesInExam(int instituteId, UUID examId, CourseType courseType,
			List<ExamCoursesPayload> examCoursesPayloads, Boolean update, UUID userId, boolean checkAccess) {

		if(checkAccess) {
			if (instituteId <= 0) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute Id"));
			}
			if (userId == null) {
				logger.error("Invalid user id");
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id"));
			}
			userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_EXAM_COURSES);
		}

		// TODO: Validate exam and courses belongs to the institute
		final ExamDetails examDetails = getExamDetails(examId, instituteId);
		final List<Course> courses = courseManager.getClassCoursesByStandardId(instituteId,
				examDetails.getStandard().getStandardId(), examDetails.getAcademicSession().getAcademicSessionId());

		final Map<UUID, Course> courseIdMap = new HashMap<>();
		for (final Course course : courses) {
			courseIdMap.put(course.getCourseId(), course);
		}
		// TODO : Also handle to skip dimensions which are not part of exam
		if (!validateCourseUpdationInExam(instituteId, examId, examCoursesPayloads, examDetails, courseIdMap)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Marks already filled for students. Cannot update max marks"));
		}
		final Map<UUID, List<ExamDimensionValuesPayload>> courseTypeExamDimensionValuesMap = new HashMap<>();
		for (final ExamCoursesPayload examCoursesPayload : examCoursesPayloads) {
			courseTypeExamDimensionValuesMap.put(examCoursesPayload.getCourseId(),
					examCoursesPayload.getDimensionValuesPayloads());
		}

		updateDefaultDimension(instituteId, examDetails.getAcademicSession().getAcademicSessionId(),
				examDetails.getStandard().getStandardId(), courseTypeExamDimensionValuesMap, courseIdMap);
		return examinationDao.addCoursesInExam(examId, courseType, examCoursesPayloads, update);
	}

	private boolean validateCourseUpdationInExam(int instituteId, UUID examId,
			List<ExamCoursesPayload> examCoursesPayloads, ExamDetails examDetails,
			final Map<UUID, Course> courseIdMap) {

		for (final ExamCoursesPayload examCoursesPayload : examCoursesPayloads) {
			final UUID courseId = examCoursesPayload.getCourseId();
			if (!courseIdMap.containsKey(courseId)) {
				logger.error("Invalid course {} provided in payload. Skipping", courseId);
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_COURSE, "Invalid course provided"));
			}

		}
		if (CollectionUtils.isEmpty(examDetails.getExamCoursesAllDimensions())) {
			return true;
		}
		final List<StudentExamMarksDetails> studentExamMarksDetails = getClassMarks(instituteId, examId, null, false);
		if (CollectionUtils.isEmpty(studentExamMarksDetails)) {
			return true;
		}

		final Map<UUID, Map<Integer, ExamDimensionValues>> courseDimensionValues = getCourseDimensionValues(
				examDetails);

		for (final ExamCoursesPayload examCoursesPayload : examCoursesPayloads) {
			final UUID courseId = examCoursesPayload.getCourseId();
			if (!courseDimensionValues.containsKey(courseId)) {
				continue;
			}
			if (checkIfMarksFilled(examId, studentExamMarksDetails, examCoursesPayload,
					courseDimensionValues.get(courseId))) {
				return false;
			}
		}
		return true;
	}

	private boolean checkIfMarksFilled(UUID examId, List<StudentExamMarksDetails> studentExamMarksDetailsList,
			ExamCoursesPayload examCoursesPayload,
			Map<Integer, ExamDimensionValues> existingExamCourseDimensionValues) {

		for (final StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			for (final ExamDimensionValuesPayload examDimensionValuesPayload : examCoursesPayload
					.getDimensionValuesPayloads()) {
				final Integer dimensionId = examDimensionValuesPayload.getDimensionId();
				if (!existingExamCourseDimensionValues.containsKey(dimensionId)) {
					continue;
				}
				/**
				 * No change in max marks
				 */
				if ((examDimensionValuesPayload.getMaxMarks() == null
						&& existingExamCourseDimensionValues.get(dimensionId).getMaxMarks() == null)
						|| (examDimensionValuesPayload.getMaxMarks() != null
								&& existingExamCourseDimensionValues.get(dimensionId).getMaxMarks() != null
								&& Double.compare(examDimensionValuesPayload.getMaxMarks(),
										existingExamCourseDimensionValues.get(dimensionId).getMaxMarks()) == 0)) {
					continue;
				}
				logger.info("payload max marks {}, existing max marks {}", examDimensionValuesPayload.getMaxMarks(),
						existingExamCourseDimensionValues.get(dimensionId).getMaxMarks());

				for (final ExamCourseMarks examCourseMarks : studentExamMarksDetails
						.getExamCoursesAllDimensionsMarks()) {
					if (!examCoursesPayload.getCourseId().equals(examCourseMarks.getCourse().getCourseId())) {
						continue;
					}
					for (final ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
							.getExamDimensionObtainedValues()) {
						if (examDimensionObtainedValues.getExamDimension().getDimensionId() != dimensionId) {
							continue;
						}

						if (examDimensionObtainedValues.getObtainedMarks() != null
								&& examDimensionObtainedValues.getObtainedMarks() >= 0d) {

							if(examDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.GRADE
								|| (examDimensionObtainedValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER &&
									!examDimensionObtainedValues.getExamDimension().isTotal())) {
								logger.info("Marks already filled for student {}, exam {}, course {}, dimension {},",
										studentExamMarksDetails.getStudent().getStudentId(), examId,
										examCoursesPayload.getCourseId(), dimensionId);
								return true;
							}
						}
					}
				}
			}
		}
		return false;
	}

	public List<ExamNode> getClassExamsForest(UUID standardId, int academicSessionId, int instituteId,
			boolean includeCourses) {
		final List<ExamNodeData> examNodeDatas = examinationDao.getExamGraphNodesByStandard(standardId,
				academicSessionId, instituteId, includeCourses);
		final List<ExamNode> classExamsForest = new ArrayList<>();
		if (CollectionUtils.isEmpty(examNodeDatas)) {
			return classExamsForest;
		}
		final Set<UUID> rootNodes = new HashSet<>();
		final Map<UUID, ExamNodeData> examMetaDataMap = new HashMap<>();

		for (final ExamNodeData examNodeData : examNodeDatas) {
			final UUID examId = examNodeData.getExamMetaData().getExamId();
			examMetaDataMap.put(examId, examNodeData);

			if (CollectionUtils.isEmpty(examNodeData.getExamMetaData().getParentIds())) {
				rootNodes.add(examId);
			}
		}

		if (CollectionUtils.isEmpty(rootNodes)) {
			logger.error("Exams are in inconsistent state. No root node found.");
			return null;
		}
		for (final UUID rootId : rootNodes) {
			final ExamNode rootNode = new ExamNode(examMetaDataMap.get(rootId).getExamMetaData(),
					examMetaDataMap.get(rootId).getCourses());
			classExamsForest.add(rootNode);
			populateExamTree(rootNode, examMetaDataMap);
		}
		return classExamsForest;
	}

	private void populateExamTree(ExamNode parentNode, final Map<UUID, ExamNodeData> examMetaDataMap) {
		final UUID examId = parentNode.getExamMetaData().getExamId();
		final Set<UUID> childIds = examMetaDataMap.get(examId).getChildIds();
		if (CollectionUtils.isEmpty(childIds)) {
			return;
		}
		final Set<ExamNode> childrenNodes = new TreeSet<>();
		for (final UUID childId : childIds) {
			final ExamNode childNode = new ExamNode(examMetaDataMap.get(childId).getExamMetaData(),
					examMetaDataMap.get(childId).getCourses());
			childrenNodes.add(childNode);
			populateExamTree(childNode, examMetaDataMap);
		}
		parentNode.setChildren(childrenNodes);
	}

	public boolean updateExamDimension(int instituteId, ExamDimension dimensionPayload) {
		if (instituteId <= 0 || dimensionPayload == null || dimensionPayload.getDimensionId() <= 0
				|| StringUtils.isBlank(dimensionPayload.getDimensionName())
				|| dimensionPayload.getDimensionType() == null || dimensionPayload.getExamEvaluationType() == null) {
			logger.error("Invalid dimension payload");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Invalid dimension updation payload"));
		}
		return examinationDao.updateExamDimension(instituteId, dimensionPayload);
	}

	public boolean deleteExamDimension(int instituteId, int dimensionId) {
		if (instituteId <= 0 || dimensionId <= 0) {
			logger.error("Invalid dimension deletion request");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Invalid dimension deletion request"));
		}
		return examinationDao.deleteExamDimension(instituteId, dimensionId);
	}

	public boolean addBulkStandardGrading(int instituteId, UUID userId, StandardMetadataPayloadReadable standardMetadataPayloadReadable) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_STANDARD_GRADING_FLAG);

		validateStandardMetadataPayloadReadable(standardMetadataPayloadReadable);

		StringBuilder incorrectStandardNames = new StringBuilder();
		List<UUID> standardIdsList = getStandardIdsByStandardName(instituteId, standardMetadataPayloadReadable.getAcademicSessionId(),
				standardMetadataPayloadReadable.getStandardNameList(), incorrectStandardNames);
		if (org.springframework.util.CollectionUtils.isEmpty(standardIdsList) || standardMetadataPayloadReadable.getStandardNameList().size() != standardIdsList.size()) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "The list of standard name is incorrect " + incorrectStandardNames));
		}

		return examinationDao.addBulkStandardGrading(instituteId, standardMetadataPayloadReadable.getAcademicSessionId(), standardIdsList, standardMetadataPayloadReadable.isScholasticGradingEnabled(), standardMetadataPayloadReadable.isCoscholasticGradingEnabled());
	}

	public void validateStandardMetadataPayloadReadable(StandardMetadataPayloadReadable standardMetadataPayloadReadable) {

		if (CollectionUtils.isEmpty(standardMetadataPayloadReadable.getStandardNameList())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD_METADATA_DETAILS, "Ensure you provide a list of standard names."));

		}
		if (standardMetadataPayloadReadable.getAcademicSessionId() <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ACADEMIC_SESSION_DETAILS, "Invalid academic session id."));
		}
		for (String standardName : standardMetadataPayloadReadable.getStandardNameList()) {
			if (StringUtils.isBlank(standardName)) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD_METADATA_DETAILS, "Ensure you provide a correct standard name."));

			}
		}
	}
	public boolean bulkStandardGradingScheme(int instituteId, int academicSessionId, UUID userId, GradeUpdatePayload gradeUpdatePayload){
		
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ACADEMIC_SESSION_DETAILS, "Invalid academic session id."));
		}
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}
		validateGradeUpdatePayload(instituteId, academicSessionId, userId, gradeUpdatePayload);
		Map<CourseType, List<StandardExaminationGrades>> courseTypeExaminationGradesMap = new HashMap<>();
		for (Map.Entry<CourseType, List<StandardExaminationGrades>> entry : gradeUpdatePayload.getCourseTypeExaminationGradesMap().entrySet()) {
			CourseType courseType = entry.getKey();
			List<StandardExaminationGrades> standardExaminationGradesList = entry.getValue();
	
			if (CollectionUtils.isEmpty(standardExaminationGradesList)) {
				courseTypeExaminationGradesMap.put(courseType, standardExaminationGradesList);
				continue;
			}
			Collections.sort(standardExaminationGradesList);
			int examinationGradeListSize = standardExaminationGradesList.size();
			List<StandardExaminationGrades> standardExaminationGradesSubList = new ArrayList<>();

			for (int i = 0; i < examinationGradeListSize; i++) {
				StandardExaminationGrades standardExaminationGrades = standardExaminationGradesList.get(i);
				standardExaminationGrades.setGradeValue(examinationGradeListSize-i);
				standardExaminationGradesSubList.add(standardExaminationGrades);
			}
			if(standardExaminationGradesList.size() != standardExaminationGradesSubList.size()){
				logger.error("size of grade value added doesn't match");
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Error Occur Try Again."));
			}
			courseTypeExaminationGradesMap.put(courseType, standardExaminationGradesSubList);
		}
		gradeUpdatePayload.setCourseTypeExaminationGradesMap(courseTypeExaminationGradesMap);
		
		return examinationDao.bulkStandardGradingScheme(instituteId, academicSessionId, ExamMarksUtils.getExaminationGradePayload(gradeUpdatePayload));
	}

	public void validateGradeUpdatePayload(int instituteId, int academicSessionId, UUID userId, GradeUpdatePayload gradeUpdatePayload){

		if(CollectionUtils.isEmpty(gradeUpdatePayload.getStandardIdList())){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Please select at least one class to add/update grades"));
		}
		if(MapUtils.isEmpty(gradeUpdatePayload.getCourseTypeExaminationGradesMap())){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "The Map Of Course Type is wrong"));
		}
		if(CollectionUtils.isEmpty(gradeUpdatePayload.getCourseTypeExaminationGradesMap().values())){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "The List of Grade Scheme is wrong"));
		}
		if(gradeUpdatePayload.getDataUpdationAction() == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Data Update Action Is Missing"));
		}
		validateDataUpdatedAction(instituteId, academicSessionId, userId, gradeUpdatePayload);
	}
	private void validateDataUpdatedAction(int instituteId, int academicSessionId, UUID userId, GradeUpdatePayload gradeUpdatePayload) {
		DataUpdationAction action = gradeUpdatePayload.getDataUpdationAction();
	
		if (action == DataUpdationAction.ADD) {
			handleAddAction(instituteId, academicSessionId, userId, gradeUpdatePayload);
		} else if (action == DataUpdationAction.UPDATE) {
			handleUpdateAction(instituteId, academicSessionId, userId, gradeUpdatePayload);
		}
	}
	
	private void handleAddAction(int instituteId, int academicSessionId, UUID userId, GradeUpdatePayload gradeUpdatePayload) {
		// Verify user authorization for adding grades
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ADD_GRADE);
	
		// Retrieve existing grades and map them by standard and course type
		List<ExamGrade> existingGrades = examinationDao.getExamGrades(instituteId, academicSessionId, null, null);
		Map<UUID, Map<CourseType, Map<String, Integer>>> gradeIdMap = ExamMarksUtils.getStandardCourseGradeMap(existingGrades);
	
		// Check if the gradeIdMap is empty
		if (MapUtils.isEmpty(gradeIdMap)) {
			return;
		}
	
		// Validate that no grades already exist for the provided standards
		validateNoConflictingGrades(gradeUpdatePayload.getStandardIdList(), gradeIdMap.keySet());
	}
	
	private void handleUpdateAction(int instituteId, int academicSessionId, UUID userId, GradeUpdatePayload gradeUpdatePayload) {
		// Verify user authorization for updating grades
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_GRADE);

		//TODO: https://embrate.atlassian.net/browse/PD-3999?atlOrigin=eyJpIjoiZTI0NjU1YTE1YTU2NDllMjkzOGIyMTAxMDBmYWNhZTEiLCJwIjoiaiJ9
		List<MarksFeedData> marksFeedDataList = examinationDao.getMarksByStandardIdOnlyGrades(academicSessionId, gradeUpdatePayload.getStandardIdList());
		if (!CollectionUtils.isEmpty(marksFeedDataList)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Grades are already assigned to students"));
		}
	}
	
	private void validateNoConflictingGrades(List<UUID> standardIds, Set<UUID> existingStandardIds) {
		if (CollectionUtils.isEmpty(standardIds)) {
			return;
		}
	
		// Check for overlap between provided standards and existing standards
		if (!Collections.disjoint(standardIds, existingStandardIds)) {
			throw new ApplicationException(
				new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS,
					"Grade(s) already present in selected class(es).")
			);
		}
	}	
	public boolean addBulkStandardGradingScheme(int instituteId, UUID userId, int academicSessionId, ExaminationGradesPayloadReadable examinationGradesPayloadReadable) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ADD_GRADE);
		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ACADEMIC_SESSION_DETAILS, "Invalid academic session id."));
		}
		validateExaminationGradesPayload(examinationGradesPayloadReadable);

		StringBuilder incorrectStandardNames = new StringBuilder();
		List<UUID> standardIdsList = examinationGradesPayloadReadable.getStandardIdList();
		if(CollectionUtils.isEmpty(standardIdsList)){
			standardIdsList = getStandardIdsByStandardName(instituteId, academicSessionId, examinationGradesPayloadReadable.getStandardNameList(), incorrectStandardNames);
		}
	
		if (org.springframework.util.CollectionUtils.isEmpty(standardIdsList) || examinationGradesPayloadReadable.getStandardNameList().size() != standardIdsList.size()) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "The list of standard name is incorrect " + incorrectStandardNames));
		}

		List<MarksFeedData> marksFeedDataList = examinationDao.getMarksByStandardIdOnlyGrades(academicSessionId, standardIdsList);
		if (!marksFeedDataList.isEmpty()) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Grades are already assigned to students"));
		}

		return examinationDao.addBulkStandardGradingScheme(instituteId, academicSessionId, standardIdsList,
				ExamMarksUtils.getExaminationGradePayload(examinationGradesPayloadReadable));
	}


	public boolean deleteBulkStandardGradingScheme(int instituteId, int academicSessionId, UUID userId, List<UUID> standardId) {
		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_USER, "Invalid user id."));
		}
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.DELETE_GRADE);
		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ACADEMIC_SESSION_DETAILS, "Invalid academic session id."));
		}
		if(CollectionUtils.isEmpty(standardId)){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_ACADEMIC_SESSION_DETAILS, "Invalid Standard id."));
		}
		//TODO: https://embrate.atlassian.net/browse/PD-3999?atlOrigin=eyJpIjoiZTI0NjU1YTE1YTU2NDllMjkzOGIyMTAxMDBmYWNhZTEiLCJwIjoiaiJ9
		List<MarksFeedData> marksFeedDataList = examinationDao.getMarksByStandardIdOnlyGrades(academicSessionId, standardId);
		if (!CollectionUtils.isEmpty(marksFeedDataList)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Grades are already assigned to students. Not Allowed to Delete the grading Scheme"));
		}

		return examinationDao.deleteStandardGradingScheme(instituteId, academicSessionId, standardId);
	}

	private List<UUID> getStandardIdsByStandardName(int instituteId, int academicSessionId, List<String> standardNameList, StringBuilder incorrectStandardNames) {
		return StandardUtils.getStandardIdsByStandardName(standardNameList,
				instituteManager.getInstituteStandardDetails(instituteId, academicSessionId), incorrectStandardNames);
	}

	public void validateExaminationGradesPayload(ExaminationGradesPayloadReadable examinationGradesPayloadReadable) {
		if (examinationGradesPayloadReadable == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Ensure you provide a list of standard names."));
		}

		if (CollectionUtils.isEmpty(examinationGradesPayloadReadable.getStandardNameList()) && CollectionUtils.isEmpty(examinationGradesPayloadReadable.getStandardIdList())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "The list of standard name can not be empty "));
		}

		if (CollectionUtils.isEmpty(examinationGradesPayloadReadable.getCourseType())) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Please provide a valid course type"));
		}
		if (CollectionUtils.isEmpty(examinationGradesPayloadReadable.getStandardExaminationGrades()) || examinationGradesPayloadReadable.getStandardExaminationGrades().size() < 2) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Please provide a valid grading scheme "));
		}
		Set<Double> existingGradeValueSet = new HashSet<>();
		for (StandardExaminationGrades standardExaminationGrades : examinationGradesPayloadReadable.getStandardExaminationGrades()) {
			if (StringUtils.isBlank(standardExaminationGrades.getGradeName())) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Please provide a valid grade name"));
			}
			if (standardExaminationGrades.getMarksRangeStart() < 0) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Please provide a valid marks range start"));
			}
			if (standardExaminationGrades.getMarksRangeEnd() <= 0) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Please provide a valid marks range end"));
			}
			if (standardExaminationGrades.getGradeValue() < 0) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Please provide a valid grade value"));
			}
			if (!existingGradeValueSet.add(standardExaminationGrades.getGradeValue())) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Please provide a unique grade value to every grade name"));
			}
		}
	}

	public boolean updateExamDimensionMapping(int instituteId, UUID examId,
			Map<CourseType, List<ExamDimensionValuesPayload>> courseTypeDimensions, UUID userId) {
		if (examId == null || MapUtils.isEmpty(courseTypeDimensions)) {
			logger.error("Invalid updation request");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Invalid updation request"));
		}

		if (userId == null) {
			logger.error("Invalid user id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_EXAM_DIMENSIONS);

		final ExamDetails examDetails = getExamDetails(examId, instituteId);

		updateDefaultDimension(instituteId, examDetails.getAcademicSession().getAcademicSessionId(),
				examDetails.getStandard().getStandardId(), courseTypeDimensions);

		return examinationDao.updateExamDimensionMapping(instituteId, examId, courseTypeDimensions);
	}

	public Boolean updateExamMetaData(ExamUpdatePayload examUpdatePayload, int instituteId, UUID userId) {
		if (instituteId <= 0 || examUpdatePayload == null || examUpdatePayload.getExamId() == null
				|| StringUtils.isBlank(examUpdatePayload.getExamName())) {
			logger.error("Invalid exam updation payload. Skipping.");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Invalid exam updation payload"));
		}

		if (userId == null) {
			logger.error("Invalid user id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_EXAM_BASIC_INFO);

		final ExamDetails examDetails = getExamDetails(examUpdatePayload.getExamId(), instituteId);
		if (examDetails == null) {
			logger.error("Exam {} does not exist in system for instituteId {}", examUpdatePayload.getExamId(),
					instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Exam does not exist in system"));
		}
		if (examDetails.getExamMetaData().getExamType() == ExamType.SYSTEM) {
			logger.error("System exam {} cannot be updated for instituteId {}", examUpdatePayload.getExamId(),
					instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"System exam cannot be updated"));
		}
		
		if (examDetails.getExamMetaData().getOperation() == null) {
			logger.error("Operation cannot be null for exam {}, instituteId {}", examUpdatePayload.getExamId(),
					instituteId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Operation cannot be null."));
		}

		return examinationDao.updateExamMetaData(examUpdatePayload);
	}

	/**
	 * This method should be called only after proper validation of marks submitted etc.
	 * Ow it can lead to orphan marks data
	 * @param instituteId
	 * @param childExamId
	 * @param newParentIds
	 * @return
	 */
	public boolean updateParentMapping(int instituteId, UUID childExamId, List<UUID> newParentIds){
		return examinationDao.updateParentMapping(instituteId, childExamId, newParentIds);
	}

	public List<MarksFeedData> getMarksByCourseId(int instituteId, UUID studentId, UUID examId, UUID courseId) {
		return examinationDao.getMarksByCourseId(instituteId, studentId, examId, courseId);
	}

	public List<MarksFeedData> getMarksByStudentId(int academicSessionId, UUID studentId) {
		return examinationDao.getMarksByStudentId(academicSessionId, studentId);
	}

	public Boolean deleteStudentAllMarksInSession(int instituteId, int academicSessionId, String admissionNumber, UUID userId){
		Student student = studentManager.getStudentByAcademicSessionAdmissionNumber(instituteId, academicSessionId, admissionNumber);
		return deleteStudentAllMarksInSession(instituteId, academicSessionId, student.getStudentId(), userId);
	}

	public Boolean deleteStudentAllMarksInSession(int instituteId, int academicSessionId, UUID studentId, UUID userId){

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.CLEAR_STUDENT_MARKS);

		List<MarksFeedData> marksFeedDataList = getMarksByStudentId(academicSessionId, studentId);
		if(CollectionUtils.isEmpty(marksFeedDataList)){
			logger.info("No marks found for studentId {}, academicSessionId {}", studentId, academicSessionId);
			return null;
		}
		Map<UUID, Set<UUID>> examCourseMap = new HashMap<>();
		for(MarksFeedData marksFeedData : marksFeedDataList){
			if(!examCourseMap.containsKey(marksFeedData.getExamId())){
				examCourseMap.put(marksFeedData.getExamId(), new HashSet<>());
			}
			examCourseMap.get(marksFeedData.getExamId()).add(marksFeedData.getCourseId());
		}
		List<ExamDimension> dimensions = getExamDimensions(instituteId);
		List<MarksFeedData> finalMarksFeedDataList = new ArrayList<>();
		for(Entry<UUID, Set<UUID>> examCoursesEntry : examCourseMap.entrySet()){
			UUID examId = examCoursesEntry.getKey();
			for(UUID courseId : examCoursesEntry.getValue()){
				MarksFeedData marksFeedData = new MarksFeedData(studentId, examId, courseId);
				List<ExamDimensionObtainedValues> examDimensionObtainedValues = new ArrayList<>();
				for(ExamDimension dimensionId : dimensions){
					examDimensionObtainedValues.add(new ExamDimensionObtainedValues(dimensionId));
				}
				marksFeedData.setExamDimensionObtainedValues(examDimensionObtainedValues);
				finalMarksFeedDataList.add(marksFeedData);
			}
		}
		return examinationDao.feedMarks(finalMarksFeedDataList, MarksFeedStatus.SAVED);
	}
	
	private List<StudentExamMarksDetails> getConfigurationBasedStudentExamMarksDetails(
			List<StudentExamMarksDetails> studentExamMarksDetailsList, StandardMetadata standardMetaData) {
		final List<StudentExamMarksDetails> configurationBasedStudentExamMarksDetails = new ArrayList<>();
		for (final StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			configurationBasedStudentExamMarksDetails
					.add(getConfigurationBasedStudentExamMarksDetails(studentExamMarksDetails, standardMetaData));
		}
		return configurationBasedStudentExamMarksDetails;
	}

	private StudentExamMarksDetails getConfigurationBasedStudentExamMarksDetails(
			StudentExamMarksDetails studentExamMarksDetails, StandardMetadata standardMetaData) {

		final Map<CourseType, List<ExamDimensionValues>> examDimensionValuesMap = studentExamMarksDetails
				.getExamDimensionValues();
		for (final Entry<CourseType, List<ExamDimensionValues>> entry : examDimensionValuesMap.entrySet()) {
			filterDimensions(entry.getKey(), entry.getValue(), standardMetaData);
		}

		final List<ExamCourseMarks> examCourseMarksList = studentExamMarksDetails.getExamCoursesMarks();
		for (final ExamCourseMarks examCourseMarks : examCourseMarksList) {
			filterDimensions(examCourseMarks.getCourse().getCourseType(),
					examCourseMarks.getExamDimensionObtainedValues(), standardMetaData);
		}
		return new StudentExamMarksDetails(studentExamMarksDetails.getStudent(),
				studentExamMarksDetails.getExamMetaData(), examDimensionValuesMap, examCourseMarksList);
	}

	public List<StudentExamMarksDetails> getClassMarksByCourseId(int instituteId, UUID examId, UUID courseId,
																 Integer sectionId) {
		//by default we r adding relieved students
		return getClassMarksByCourseId(instituteId, 0, examId, courseId, null, sectionId, true, null);
	}

	/**
	 * This method gives marks added for given exam and filters the dimensions
	 * based on the configurations
	 *
	 * @param instituteId
	 * @param examId
	 * @param courseId
	 * @return
	 */
	public List<StudentExamMarksDetails> getClassMarksByCourseId(int instituteId, int academicSessionId, UUID examId, UUID courseId,
			UUID standardId, Integer sectionId, boolean addRelievedStudents, UUID userId) {

		if(instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		if(examId == null) {
			logger.error("Invalid exam id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid exam Id"));
		}

		/**
		 * Assuming students will belong to a single class when marks is filled from MOBILE or WEB
		 */
		if(academicSessionId <= 0 || standardId == null) {
			ExamDetails examDetails = getExamDetails(examId, instituteId);
			if(academicSessionId <= 0) {
				academicSessionId = examDetails.getAcademicSession().getAcademicSessionId();
			}
			if(standardId == null) {
				standardId = examDetails.getStandard().getStandardId();
			}
		}

		/**
		 * for backward compatibility of mobile app
		 */
		if(userId != null) {
			if(!verifyExaminationDataAccessibility(instituteId, academicSessionId, standardId, sectionId, courseId, userId, false)) {
				/**
				 * if user don't access to view all classes exam marks data and is not assigned teacher for current class + section + course, then return empty marks list
				 */
				return new ArrayList<>();
			}
		}

		final List<StudentExamMarksDetails> studentExamMarksDetailList = getFilteredCourseStudents(courseId,
				examinationDao.getClassMarks(instituteId, examId, courseId, sectionId, addRelievedStudents, false));

		if (CollectionUtils.isEmpty(studentExamMarksDetailList)) {
			return new ArrayList<>();
		}

		final StandardMetadata standardMetaData = examinationDao.getStandardMetaData(instituteId, examId);

		final List<StudentExamMarksDetails> configurationBasedStudentExamMarksDetails = getConfigurationBasedStudentExamMarksDetails(
				studentExamMarksDetailList, standardMetaData);

		Collections.sort(configurationBasedStudentExamMarksDetails, new Comparator<StudentExamMarksDetails>() {

			@Override
			public int compare(StudentExamMarksDetails s1, StudentExamMarksDetails s2) {
				return s1.getStudent().getStudentBasicInfo().getName()
						.compareToIgnoreCase(s2.getStudent().getStudentBasicInfo().getName());
			}
		});

		return configurationBasedStudentExamMarksDetails;
	}


	public ExamDimensionStudentMarksDetails getClassMarksByCourseExamDimensionIds(int instituteId, int academicSessionId,
					UUID standardId, Integer sectionId, CourseType courseType, UUID courseId, UUID examId, int dimensionId,
						 boolean addRelievedStudents, UUID userId, StudentSortingParameters studentSortingParameters) {

		List<StudentExamMarksDetails> studentExamMarksDetailsList = getClassMarksByCourseId(
				instituteId, academicSessionId, examId, courseId, standardId, sectionId, addRelievedStudents, userId);

		if(CollectionUtils.isEmpty(studentExamMarksDetailsList)) {
			return null;
		}
		List<ExamGrade> examGradeList = getExamGrades(instituteId, academicSessionId, standardId, courseType);
		List<ExamDimensionValues> examDimensionValuesList = studentExamMarksDetailsList
				.get(0) == null ? null : studentExamMarksDetailsList
				.get(0).getExamDimensionValues() == null ? null : studentExamMarksDetailsList
				.get(0).getExamDimensionValues().get(courseType) == null ? null : studentExamMarksDetailsList
						.get(0).getExamDimensionValues().get(courseType);

		if(CollectionUtils.isEmpty(examDimensionValuesList)) {
			return null;
		}

		ExamDimensionValues examDimensionValues = null;
		for(ExamDimensionValues examDimensionValue : examDimensionValuesList) {
			if(examDimensionValue.getExamDimension().getDimensionId() == dimensionId) {
				examDimensionValues = examDimensionValue;
				break;
			}
		}

		if(examDimensionValues == null) {
			return null;
		}

		List<StudentMarksDetails> studentMarksDetailsList = new ArrayList<>();
		for(StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			Student student = studentExamMarksDetails.getStudent();
			for(ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesMarks()) {
				if(examCourseMarks.getCourse().getCourseId().equals(courseId)) {
					for(ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()) {
						if(examDimensionObtainedValues.getExamDimension().getDimensionId() == dimensionId) {
							studentMarksDetailsList.add(new StudentMarksDetails(
									Student.getStudentLite(student), examDimensionObtainedValues
							));
						}
					}
				}
			}
		}

		ExamDimensionStudentMarksDetails examDimensionStudentMarksDetails = new ExamDimensionStudentMarksDetails(
				examDimensionValues, examGradeList,
				CollectionUtils.isEmpty(studentMarksDetailsList) ? null : studentMarksDetailsList);

		StudentSorter.sortStudents(
				examDimensionStudentMarksDetails.getStudentMarksDetailsList(),
				studentSortingParameters,
				new StudentSorter.StudentKeyExtractor<StudentMarksDetails>() {
					@Override
					public String getAdmissionNumber(StudentMarksDetails studentMarksDetails) {
						return studentMarksDetails.getStudentLite().getAdmissionNumber();
					}

					@Override
					public String getName(StudentMarksDetails studentMarksDetails) {
						return studentMarksDetails.getStudentLite().getName();
					}

					@Override
					public String getRollNumber(StudentMarksDetails studentMarksDetails) {
						return studentMarksDetails.getStudentLite().getStudentSessionData().getRollNumber();
					}

					@Override
					public String getSectionName(StudentMarksDetails studentMarksDetails) {
						StandardSections section = studentMarksDetails.getStudentLite().getStudentSessionData().getStandardSection();
						return section != null ? section.getSectionName() : null;
					}

					@Override
					public Integer getAdmissionDate(StudentMarksDetails studentMarksDetails) {
						return studentMarksDetails.getStudentLite().getAdmissionDate();
					}
				}
		);


		return examDimensionStudentMarksDetails;
	}
	public ExamDimensionDataStudentMarksDetails getClassMarksByCourseExamDimensionIdsList(int instituteId, int academicSessionId,
					UUID standardId, Integer sectionId, CourseType courseType, UUID courseId, UUID examId, Set<Integer> dimensionIdSet,
						 boolean addRelievedStudents, UUID userId) {

		List<StudentExamMarksDetails> studentExamMarksDetailsList = getClassMarksByCourseId(
				instituteId, academicSessionId, examId, courseId, standardId, sectionId, addRelievedStudents, userId);

		if(CollectionUtils.isEmpty(studentExamMarksDetailsList)) {
			return null;
		}
		List<ExamGrade> examGradeList = getExamGrades(instituteId, academicSessionId, standardId, courseType);
		List<ExamDimensionValues> examDimensionValuesList = studentExamMarksDetailsList
				.get(0) == null ? null : studentExamMarksDetailsList
				.get(0).getExamDimensionValues() == null ? null : studentExamMarksDetailsList
				.get(0).getExamDimensionValues().get(courseType) == null ? null : studentExamMarksDetailsList
						.get(0).getExamDimensionValues().get(courseType);

		if(CollectionUtils.isEmpty(examDimensionValuesList)) {
			return null;
		}

		Map<Integer,ExamDimensionValues> examDimensionValues = new LinkedHashMap<>();
		List<StudentMarksDetailsDimensionsData> studentMarksDetailsList = new ArrayList<>();
		for(StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			List<ExamDimensionObtainedValues> examDimensionObtainedValuesList = new ArrayList<>();
			Student student = studentExamMarksDetails.getStudent();
			for(ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesMarks()) {
				if(!examCourseMarks.getCourse().getCourseId().equals(courseId)) {
					continue;
					}
				for(ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()) {
					int dimensionId = examDimensionObtainedValues.getExamDimension().getDimensionId();
					if(!dimensionIdSet.contains(dimensionId)) {
							continue;
						}
						ExamDimensionValues examDimensionValueData = new ExamDimensionValues(examDimensionObtainedValues.getExamDimension(),examDimensionObtainedValues.getOriginalMaxMarks(),examDimensionObtainedValues.getMaxMarks(),examDimensionObtainedValues.getMinMarks(),examDimensionObtainedValues.getMaxGrade(),examDimensionObtainedValues.getMinGrade(),examDimensionObtainedValues.getExamCoursePublishedStatus());
						examDimensionValues.putIfAbsent(dimensionId, examDimensionValueData);
						examDimensionObtainedValuesList.add(examDimensionObtainedValues);
				}
				studentMarksDetailsList.add(new StudentMarksDetailsDimensionsData(
								Student.getStudentLite(student), examDimensionObtainedValuesList
						));
						break;
			}
		}


		ExamDimensionDataStudentMarksDetails examDimensionStudentMarksDetails = new ExamDimensionDataStudentMarksDetails(
				new ArrayList<>(examDimensionValues.values()), examGradeList,
				CollectionUtils.isEmpty(studentMarksDetailsList) ? null : studentMarksDetailsList);

		return examDimensionStudentMarksDetails;
	}

	public List<StudentExamMarksDetails> getClassMarksByCourseList(int instituteId, int academicSessionId, UUID examId, Set<UUID> courseIdSets,
																 UUID standardId, Integer sectionId, boolean addRelievedStudents, UUID userId) {

		if(instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		if(examId == null) {
			logger.error("Invalid exam id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid exam Id"));
		}

		/**
		 * Assuming students will belong to a single class when marks is filled from MOBILE or WEB
		 */
		if(academicSessionId <= 0 || standardId == null) {
			ExamDetails examDetails = getExamDetails(examId, instituteId);
			if(academicSessionId <= 0) {
				academicSessionId = examDetails.getAcademicSession().getAcademicSessionId();
			}
			if(standardId == null) {
				standardId = examDetails.getStandard().getStandardId();
			}
		}

		Set<UUID> finalCourseIdSet = new HashSet<>();
		/**
		 * for backward compatibility of mobile app
		 */
		if(userId != null) {
			if (!CollectionUtils.isEmpty(courseIdSets)) {
				for (UUID courseId : courseIdSets) {
					if (verifyExaminationDataAccessibility(instituteId, academicSessionId, standardId, sectionId, courseId, userId, false)) {
						/**
						 * filtering out standard+section+courses of which staff have access to update marks
						 */
						finalCourseIdSet.add(courseId);
					}
				}
			} else {
				String standardSectionIdStr = standardId + (sectionId == null ? "" : ":" + sectionId);
				Map<String, Set<UUID>> staffStandardSectionStrCourseDetailsMap = timetableManager.getStaffStandardSectionIdEntityDetails(
						instituteId, academicSessionId, userId);

				if(sectionId == null || sectionId <= 0) {
					Map<String, Set<UUID>> staffStandardCourseDetailsMap = new HashMap<>();
					for(Entry<String, Set<UUID>> staffStandardCourseDetailsEntry : staffStandardSectionStrCourseDetailsMap.entrySet()) {
						String[] token = staffStandardCourseDetailsEntry.getKey().split(":");
						staffStandardCourseDetailsMap.put(token[0], staffStandardCourseDetailsEntry.getValue());
					}
					staffStandardSectionStrCourseDetailsMap = new HashMap<>(staffStandardCourseDetailsMap);
				}

				if (!MapUtils.isEmpty(staffStandardSectionStrCourseDetailsMap) &&
						!CollectionUtils.isEmpty(staffStandardSectionStrCourseDetailsMap.get(standardSectionIdStr))) {
					finalCourseIdSet = new HashSet<>(staffStandardSectionStrCourseDetailsMap.get(standardSectionIdStr));
				}
			}
		}

		/**
		 * if final courses id set is empty i.e. you have no course assigned nd you don;t have access to view all classes exam marks data, then return empty.
		 */
		if(CollectionUtils.isEmpty(finalCourseIdSet) && !userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ACCESS_ALL_CLASSES_EXAMINATION_MARKS_DATA, false)) {
			return new ArrayList<>();
		}

		final List<StudentExamMarksDetails> studentExamMarksDetailList = getFilteredCourseStudents(finalCourseIdSet,
				examinationDao.getClassMarks(instituteId, examId, finalCourseIdSet, sectionId, addRelievedStudents, false));

		if (CollectionUtils.isEmpty(studentExamMarksDetailList)) {
			return new ArrayList<>();
		}

		final StandardMetadata standardMetaData = examinationDao.getStandardMetaData(instituteId, examId);

		final List<StudentExamMarksDetails> configurationBasedStudentExamMarksDetails = getConfigurationBasedStudentExamMarksDetails(
				studentExamMarksDetailList, standardMetaData);

		Collections.sort(configurationBasedStudentExamMarksDetails, new Comparator<StudentExamMarksDetails>() {
			@Override
			public int compare(StudentExamMarksDetails s1, StudentExamMarksDetails s2) {
				return s1.getStudent().getStudentBasicInfo().getName()
						.compareToIgnoreCase(s2.getStudent().getStudentBasicInfo().getName());
			}
		});

		for(StudentExamMarksDetails studentExamMarksDetails : configurationBasedStudentExamMarksDetails) {

			Collections.sort(studentExamMarksDetails.getExamCoursesAllDimensionsMarks(), new Comparator<ExamCourseMarks>() {
				@Override
				public int compare(ExamCourseMarks s1, ExamCourseMarks s2) {
					return s1.getCourse().compareTo(s2.getCourse());
				}
			});

		}

		return configurationBasedStudentExamMarksDetails;
	}

	/**
	 * This method removes students from list if given course is not assigned to
	 * it
	 *
	 * @param courseId
	 * @param studentExamMarksDetailList
	 * @return
	 */
	private List<StudentExamMarksDetails> getFilteredCourseStudents(UUID courseId,
			List<StudentExamMarksDetails> studentExamMarksDetailList) {

		/**
		 * Not handling scenario for all users filtering
		 */
		if (courseId == null) {
			return studentExamMarksDetailList;
		}
		final CourseStudents courseStudents = courseManager.getStudentsByCourse(courseId);
		if (CollectionUtils.isEmpty(studentExamMarksDetailList) || courseStudents == null
				|| courseStudents.getCourse().isMandatory()) {
			return studentExamMarksDetailList;
		}

		final List<StudentExamMarksDetails> filteredStudentExamMarksDetails = new ArrayList<>();
		if (CollectionUtils.isEmpty(courseStudents.getStudents())) {
			return filteredStudentExamMarksDetails;
		}
		for (final StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailList) {
			if (courseStudents.getStudents().contains(studentExamMarksDetails.getStudent().getStudentId())) {
				filteredStudentExamMarksDetails.add(studentExamMarksDetails);
			}
		}
		return filteredStudentExamMarksDetails;
	}

	private List<StudentExamMarksDetails> getFilteredCourseStudents(Set<UUID> courseIdSet,
				List<StudentExamMarksDetails> studentExamMarksDetailList) {

		/**
		 * Not handling scenario for all users filtering
		 */
		if (CollectionUtils.isEmpty(courseIdSet)) {
			return studentExamMarksDetailList;
		}
		final List<CourseStudents> courseStudentsList = courseManager.getStudentsByCourse(courseIdSet);

		if (CollectionUtils.isEmpty(studentExamMarksDetailList) || CollectionUtils.isEmpty(courseStudentsList)) {
			return studentExamMarksDetailList;
		}

		final Set<UUID> finalStudentUUIDs = new HashSet<UUID>();
		for(CourseStudents courseStudents : courseStudentsList) {
			finalStudentUUIDs.addAll(courseStudents.getStudents());
		}

		final List<StudentExamMarksDetails> filteredStudentExamMarksDetails = new ArrayList<>();
		if (CollectionUtils.isEmpty(finalStudentUUIDs)) {
			return filteredStudentExamMarksDetails;
		}
		for (final StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailList) {
			if (finalStudentUUIDs.contains(studentExamMarksDetails.getStudent().getStudentId())) {
				filteredStudentExamMarksDetails.add(studentExamMarksDetails);
			}
		}
		return filteredStudentExamMarksDetails;
	}

	public List<StudentExamMarksDetails> getClassMarks(int instituteId, UUID examId, Integer sectionId,
			boolean filterDimensions) {
		final List<StudentExamMarksDetails> studentExamMarksDetailsList = examinationDao.getClassMarks(instituteId,
				examId, sectionId, false);
		if (!filterDimensions) {
			return studentExamMarksDetailsList;
		}

		final StandardMetadata standardMetaData = examinationDao.getStandardMetaData(instituteId, examId);
		return getConfigurationBasedStudentExamMarksDetails(studentExamMarksDetailsList, standardMetaData);

	}

	public List<StudentExamMarksDetails> getResolvedClassMarks(int instituteId, int academicSessionId, UUID examId, UUID courseId,
															   UUID standardId, Integer sectionId, boolean addRelievedStudents, UUID userId) {

		if(instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		if(examId == null) {
			logger.error("Invalid exam id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid exam Id"));
		}

//		if(courseId == null) {
//			logger.error("Invalid course id");
//			throw new ApplicationException(
//					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid course Id"));
//		}

		/**
		 * Assuming students will belong to a single class when marks is filled from MOBILE or WEB
		 */
		if(academicSessionId <= 0 || standardId == null) {
			ExamDetails examDetails = getExamDetails(examId, instituteId);
			if(academicSessionId <= 0) {
				academicSessionId = examDetails.getAcademicSession().getAcademicSessionId();
			}
			if(standardId == null) {
				standardId = examDetails.getStandard().getStandardId();
			}
		}


		/**
		 * for backward compatibility of mobile app
		 */
		if(userId != null) {
			if(!verifyExaminationDataAccessibility(instituteId, academicSessionId, standardId, sectionId, courseId, userId, false)) {
				/**
				 * if user don't access to view all classes exam marks data and is not assigned teacher for current class + section + course, then return empty marks list
				 */
				return new ArrayList<>();
			}
		}

		List<StudentExamMarksDetails> studentExamMarksDetailsList =  getResolvedClassMarksMap(
				instituteId, examId, courseId, sectionId == null ? null : new HashSet<>(Arrays.asList(sectionId)), false, false).get(examId);

		if(addRelievedStudents) {
			return studentExamMarksDetailsList;
		}

		List<StudentExamMarksDetails> finalStudentExamMarksDetailsList = new ArrayList<>();
		for(StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			if(studentExamMarksDetails.getStudent().getStudentStatus() == StudentStatus.ENROLLED) {
				finalStudentExamMarksDetailsList.add(studentExamMarksDetails);
			}
		}
		return finalStudentExamMarksDetailsList;
	}

	/**
	 * Gets marks for required exam id after resolving through hierarchy. It
	 * also filters the dimensions based on configurations
	 *
	 * @param instituteId
	 * @param marksRequiredExamId
	 * @param sectionId
	 * @return
	 */
	public List<StudentExamMarksDetails> getResolvedClassMarks(int instituteId, UUID marksRequiredExamId,
			Integer sectionId, boolean filterOnBasisOfMarksSubmitted) {
		return getResolvedClassMarks(instituteId, marksRequiredExamId, null, sectionId == null ? null : new HashSet<>(Arrays.asList(sectionId)), false);
	}

	public List<StudentExamMarksDetails> getResolvedClassMarks(int instituteId, UUID marksRequiredExamId, UUID courseId,
															   Set<Integer> sectionIdSet, boolean filterOnBasisOfMarksSubmitted) {
		return getResolvedClassMarksMap(instituteId, marksRequiredExamId, courseId, sectionIdSet, false, filterOnBasisOfMarksSubmitted).get(marksRequiredExamId);
	}

	/**
	 * Gets marks for required exam id after resolving through hierarchy. It
	 * also filters the dimensions based on configurations
	 *
	 * @param instituteId
	 * @param marksRequiredExamId
	 * @param courseId
	 * @return
	 */
	public Map<UUID, List<StudentExamMarksDetails>> getResolvedClassMarksMap(int instituteId, UUID marksRequiredExamId, UUID courseId,
																			 Set<Integer> sectionIdSet, boolean resultContainChildNodes, boolean filterOnBasisOfMarksSubmitted) {

		final List<ExamNodeData> examNodeDatas = examinationDao.getExamGraphNodesByExam(marksRequiredExamId,
				instituteId, false);
		if (CollectionUtils.isEmpty(examNodeDatas)) {
			logger.error("No exams meta data found for exam {}, institute {}", marksRequiredExamId, instituteId);
			return new HashMap<>();
		}

		final List<ExamDetails> examDetailsList = getAllExamDetailsInStandardByExamId(instituteId, marksRequiredExamId);

		if (CollectionUtils.isEmpty(examDetailsList)) {
			logger.error("No exams details found for exam {}, institute {}", marksRequiredExamId, instituteId);
			return new HashMap<>();
		}

		final Map<UUID, ExamNodeData> examMetaDataMap = new HashMap<>();

		for (final ExamNodeData examNodeData : examNodeDatas) {
			final UUID examId = examNodeData.getExamMetaData().getExamId();
			examMetaDataMap.put(examId, examNodeData);
		}

		final ExamNode rootNode = new ExamNode(examMetaDataMap.get(marksRequiredExamId).getExamMetaData(),
				examMetaDataMap.get(marksRequiredExamId).getCourses());

		final Map<UUID, ExamDetails> examDetailsMap = getExamDetailsMap(examDetailsList);

		final ExamDetails anyExamDetails = examDetailsList.get(0);

		final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = getExamGrades(instituteId,
				anyExamDetails.getAcademicSession().getAcademicSessionId(),
				anyExamDetails.getStandard().getStandardId());

		/**
		 * StandardMetaData will be same for all exams in particular standard of
		 * a session
		 */
		final StandardMetadata standardMetadata = examDetailsList.get(0).getStandardsMetaData();

		Map<UUID, List<StudentExamMarksDetails>> studentExamMarksDetailsList = getResolvedCourseMarks(instituteId, rootNode,
				null, examMetaDataMap, examDetailsMap, courseTypeExamGrades, courseId, sectionIdSet, standardMetadata, filterOnBasisOfMarksSubmitted);

		if (MapUtils.isEmpty(studentExamMarksDetailsList)) {
			return new HashMap<>();
		}

		if(!resultContainChildNodes) {
			List<StudentExamMarksDetails> studentExamMarksDetailList = studentExamMarksDetailsList.get(marksRequiredExamId);
			studentExamMarksDetailsList = new HashMap<UUID, List<StudentExamMarksDetails>>();
			studentExamMarksDetailsList.put(marksRequiredExamId, studentExamMarksDetailList);
		}



		Map<UUID, List<StudentExamMarksDetails>> studentExamMarksDetailsMap = new HashMap<UUID, List<StudentExamMarksDetails>>();
		for(Entry<UUID, List<StudentExamMarksDetails>> studentExamMarksDetailsEntry : studentExamMarksDetailsList.entrySet()) {
			studentExamMarksDetailsMap.put(studentExamMarksDetailsEntry.getKey(), getFilteredCourseStudents(
					courseId, getConfigurationBasedStudentExamMarksDetails(studentExamMarksDetailsEntry.getValue(), standardMetadata)));
		}

		for(Entry<UUID, List<StudentExamMarksDetails>> entry : studentExamMarksDetailsMap.entrySet()) {
			Collections.sort(entry.getValue(), new Comparator<StudentExamMarksDetails>() {

				@Override
				public int compare(StudentExamMarksDetails s1, StudentExamMarksDetails s2) {
					return s1.getStudent().getStudentBasicInfo().getName()
							.compareToIgnoreCase(s2.getStudent().getStudentBasicInfo().getName());
				}
			});
		}

		return studentExamMarksDetailsMap;

	}

	public List<StandardGradeDetails> getGradeSchemeWithStandard(int instituteId, int academicSessionId){
		
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Invalid Institute."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Invalid Academic Session"));
		}

		List<ExamGrade> examGrades = examinationDao.getExamGrades(instituteId, academicSessionId, null, null);
		List<Standard> standardPayloadList = instituteManager.getInstituteStandardList(instituteId, academicSessionId);
		if(CollectionUtils.isEmpty(standardPayloadList)){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Invalid Standard Data"));
		}
		Map<UUID, Map<CourseType, List<StandardExaminationGrades>>> gradeSchemeWithStandardDetails = StandardExaminationGrades.convertExamGradeListToMap(examGrades);
		
		List<StandardGradeDetails> standardGradeManagementDataList = new ArrayList<StandardGradeDetails>();
		
		for(Standard standard : standardPayloadList){
			UUID standardId = standard.getStandardId();
			Map<CourseType, List<StandardExaminationGrades>> grades = new HashMap<>();
			if(gradeSchemeWithStandardDetails.containsKey(standardId)){
				grades = gradeSchemeWithStandardDetails.get(standardId);
			}
			standardGradeManagementDataList.add(new StandardGradeDetails(standard, grades));
		}
		
		return standardGradeManagementDataList;
	}

	public boolean updateGradeScheme(int instituteId, int academicSessionId, UUID userId, GradeRenamePayload gradeRenamePayload){
		
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Invalid Institute."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Invalid Academic Session"));
		}

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Invalid User Id."));
		}
		validateGradeRenamePayload(gradeRenamePayload);
		
		List<ExamGrade> examGrades = examinationDao.getExamGrades(instituteId, academicSessionId, null, null);
		Map<UUID, Map<CourseType, Map<String, Integer>>> standardCourseTypeGradeIdMap = ExamMarksUtils.getStandardCourseGradeMap(examGrades);
		if(MapUtils.isEmpty(standardCourseTypeGradeIdMap)){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Grade Data Not Found"));
		}

		Map<Integer, StandardExaminationGrades> updateGradeFormatedData = buildUpdatedGradeDataMap(gradeRenamePayload, standardCourseTypeGradeIdMap);
		
		return examinationDao.updateGradeScheme(updateGradeFormatedData);
	}

	private Map<Integer, StandardExaminationGrades> buildUpdatedGradeDataMap(GradeRenamePayload gradeRenamePayload, Map<UUID, Map<CourseType, Map<String, Integer>>> standardCourseTypeGradeIdMap) {
		Map<Integer, StandardExaminationGrades> updateGradeFormatedData = new HashMap<>();
		for (UUID standardId : gradeRenamePayload.getStandardIdList()) {
			for (CourseType courseType : gradeRenamePayload.getCourseTypeExaminationGradesMap().keySet()) {
				Map<String, StandardExaminationGrades> standardExaminationGradesMap = gradeRenamePayload.getCourseTypeExaminationGradesMap().get(courseType);
				for (String key : standardExaminationGradesMap.keySet()) {
					Integer gradeId = standardCourseTypeGradeIdMap.get(standardId).get(courseType).get(key);
					if (gradeId == null) {
						continue;
					}
					updateGradeFormatedData.put(gradeId, standardExaminationGradesMap.get(key));
				}
			}
		}
		return updateGradeFormatedData;
	}

	private void validateGradeRenamePayload(GradeRenamePayload gradeRenamePayload){
		if(gradeRenamePayload == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Invalid Grade Data Entered"));
		}
		if(CollectionUtils.isEmpty(gradeRenamePayload.getStandardIdList())){
			throw new ApplicationException(
				new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Invalid standardId"));
		}
		if(MapUtils.isEmpty(gradeRenamePayload.getCourseTypeExaminationGradesMap())){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Invalid Course Type Data"));
		}
		if(CollectionUtils.isEmpty(gradeRenamePayload.getCourseTypeExaminationGradesMap().values())){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Invalid Update Data"));
		}

	 
		 for (CourseType courseType : gradeRenamePayload.getCourseTypeExaminationGradesMap().keySet()) {
			// Ensure uniqueness of gradeName and rangeDisplayName
			Set<String> gradeNames = new HashSet<>();
			Set<String> rangeDisplayNames = new HashSet<>();
			Map<String, StandardExaminationGrades> gradesMap = gradeRenamePayload.getCourseTypeExaminationGradesMap().get(courseType);
	 
			 for (StandardExaminationGrades grade : gradesMap.values()) {
				 // Check for duplicate gradeName
				 if (!gradeNames.add(grade.getGradeName())) {
					 throw new ApplicationException(
							 new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Duplicate gradeName found: " + grade.getGradeName()));
				 }
	 
				 // Check for duplicate rangeDisplayName
				 if (!rangeDisplayNames.add(grade.getRangeDisplayName())) {
					 throw new ApplicationException(
							 new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "Duplicate rangeDisplayName found: " + grade.getRangeDisplayName()));
				 }
			 }
		}
	}

	private Map<UUID, ExamDetails> getExamDetailsMap(List<ExamDetails> examDetailsList) {
		final Map<UUID, ExamDetails> examDetailsMap = new HashMap<>();
		for (final ExamDetails examDetails : examDetailsList) {
			examDetailsMap.put(examDetails.getExamMetaData().getExamId(), examDetails);
		}
		return examDetailsMap;
	}

	private Map<UUID, List<StudentExamMarksDetails>> getResolvedCourseMarks(int instituteId, ExamNode examNode, UUID parentExamId,
			final Map<UUID, ExamNodeData> examMetaDataMap, Map<UUID, ExamDetails> examDetailsMap,
			Map<CourseType, List<ExamGrade>> courseTypeExamGrades, UUID courseId, Set<Integer> sectionIdSet, StandardMetadata standardMetadata, boolean filterOnBasisOfMarksSubmitted) {
		final UUID examId = examNode.getExamMetaData().getExamId();

		final ExamDetails examDetails = examDetailsMap.get(examId);
		if (examDetails == null) {
			logger.error("Invalid exam configuration. Exam {} does not exists", examId);
			throw new EmbrateRunTimeException("Invalid exam configuration. Exam does not exists");
		}

		Map<UUID, List<StudentExamMarksDetails>> studentExamMarksDetailsMap = new HashMap<UUID, List<StudentExamMarksDetails>>();
		final Set<UUID> childIds = examMetaDataMap.get(examId).getChildIds();

		if (CollectionUtils.isEmpty(childIds)) {
			final List<StudentExamMarksDetails> studentExamMarksDetails = filterStudentExamMarksDetailsBySectionIds(examinationDao.getClassMarks(instituteId,
					examId, courseId, null, filterOnBasisOfMarksSubmitted), sectionIdSet);
			studentExamMarksDetailsMap.put(examId, mapParentDimensions(instituteId, studentExamMarksDetails, examDetailsMap.get(parentExamId), false, standardMetadata));
			return studentExamMarksDetailsMap;
		}

		/**
		 * TODO : Fetch marks for all exams required in sub graph based on exam
		 * id. Will save many dao calls.
		 */
		final List<StudentExamMarksDetails> studentExamMarksDetails = filterStudentExamMarksDetailsBySectionIds(examinationDao.getClassMarks(instituteId, examId,
				courseId, null, filterOnBasisOfMarksSubmitted), sectionIdSet);

		final List<List<StudentExamMarksDetails>> childrenStudentExamMarksDetailsList = new ArrayList<>();
		for (final UUID childId : childIds) {
			Map<UUID, List<StudentExamMarksDetails>> map = getResolvedCourseMarks(instituteId,
					new ExamNode(examMetaDataMap.get(childId).getExamMetaData(), examMetaDataMap.get(childId).getCourses()),
					examId, examMetaDataMap, examDetailsMap, courseTypeExamGrades, courseId, sectionIdSet, standardMetadata, filterOnBasisOfMarksSubmitted);
			childrenStudentExamMarksDetailsList.add(map.get(childId));
			studentExamMarksDetailsMap.putAll(map);
		}

		IMarksComputator marksComputator = marksComputationFactory
				.getMarksComputator(examNode.getExamMetaData().getOperation());

		final List<StudentExamMarksDetails> childrenCombinedStudentExamMarksDetails = mergeWithParentExamMarks(
				studentExamMarksDetails, courseTypeExamGrades, marksComputator.compute(childrenStudentExamMarksDetailsList,
				studentExamMarksDetails, courseTypeExamGrades));

		/**
		 * Scaling combined child marks to current exam max marks. There will
		 * not be any dimension reduction, only scaling will happen as
		 * dimensions of child exams will be already mapped
		 */
		final List<StudentExamMarksDetails> scaledChildrenCombinedStudentExamMarksDetails = mapParentDimensions(
				instituteId, childrenCombinedStudentExamMarksDetails, examDetailsMap.get(examId), true, standardMetadata);

		studentExamMarksDetailsMap.put(examId, mapParentDimensions(instituteId, scaledChildrenCombinedStudentExamMarksDetails, examDetailsMap.get(parentExamId),
				false, standardMetadata));

		return studentExamMarksDetailsMap;
	}

	private List<StudentExamMarksDetails> filterStudentExamMarksDetailsBySectionIds(List<StudentExamMarksDetails> studentExamMarksDetailsList,
																					Set<Integer> sectionIdSet) {
		if(CollectionUtils.isEmpty(studentExamMarksDetailsList) || CollectionUtils.isEmpty(sectionIdSet)) {
			return studentExamMarksDetailsList;
		}
		List<StudentExamMarksDetails> finalStudentExamMarksDetailsList = new ArrayList<>();
		for(StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			List<StandardSections> standardSectionList = studentExamMarksDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
			if(CollectionUtils.isEmpty(standardSectionList)) {
				continue;
			}
			int sectionId = standardSectionList.get(0).getSectionId();
			if(!sectionIdSet.contains(sectionId)) {
				continue;
			}
			finalStudentExamMarksDetailsList.add(studentExamMarksDetails);
		}
		return finalStudentExamMarksDetailsList;
	}

	private List<StudentExamMarksDetails> mergeWithParentExamMarks(
			List<StudentExamMarksDetails> parentStudentExamMarksDetails,
			Map<CourseType, List<ExamGrade>> courseTypeExamGrades,
			final Map<UUID, Map<UUID, Map<Integer, ExamDimensionObtainedValues>>> studentCourseDimensionObtainedValues) {

		final List<StudentExamMarksDetails> combinedStudentExamMarksDetails = new ArrayList<>();
		for (final StudentExamMarksDetails studentExamMarksDetails : parentStudentExamMarksDetails) {
			final UUID studentId = studentExamMarksDetails.getStudent().getStudentId();
			if (!studentCourseDimensionObtainedValues.containsKey(studentId)) {
				combinedStudentExamMarksDetails.add(studentExamMarksDetails);
				continue;
			}
			final List<ExamCourseMarks> updatedExamCoursesMarks = new ArrayList<>();
			for (final ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {
				final UUID courseId = examCourseMarks.getCourse().getCourseId();
				if (!studentCourseDimensionObtainedValues.get(studentId).containsKey(courseId)) {
					updatedExamCoursesMarks.add(examCourseMarks);
					continue;
				}
				final List<ExamDimensionObtainedValues> examDimensionObtainedValuesList = new ArrayList<>();
				for (final ExamDimensionObtainedValues examDimensionObtainedValues : studentCourseDimensionObtainedValues
						.get(studentId).get(courseId).values()) {
					final ExamDimension examDimension = examDimensionObtainedValues.getExamDimension();

					if (examDimension.getExamEvaluationType() == ExamEvaluationType.GRADE) {
						final Double gradeValueCount = examDimensionObtainedValues.getMaxGrade() == null ? null
								: examDimensionObtainedValues.getMaxGrade().getGradeValue();
						final Double gradeValueSum = examDimensionObtainedValues.getObtainedGrade() == null ? null
								: examDimensionObtainedValues.getObtainedGrade().getGradeValue();
						ExamGrade netObtainedGrade = null;
						if (gradeValueCount != null && gradeValueSum != null && Double.compare(gradeValueCount, 0) > 0
								&& Double.compare(gradeValueSum, 0) > 0) {
							final Double netGradeValue = gradeValueSum / gradeValueCount;
							netObtainedGrade = ExamMarksUtils.getExamGradeByValue(
									courseTypeExamGrades.get(examCourseMarks.getCourse().getCourseType()),
									netGradeValue);
						}
						/**
						 * Resetting max grade to null
						 */
						examDimensionObtainedValues.setMaxGrade(null);
						examDimensionObtainedValues.setObtainedGrade(netObtainedGrade);
					}
					examDimensionObtainedValuesList.add(examDimensionObtainedValues);

				}
				updatedExamCoursesMarks
						.add(new ExamCourseMarks(examCourseMarks.getCourse(), examDimensionObtainedValuesList));
			}
			combinedStudentExamMarksDetails.add(new StudentExamMarksDetails(studentExamMarksDetails.getStudent(),
					studentExamMarksDetails.getExamMetaData(), studentExamMarksDetails.getExamDimensionValues(),
					updatedExamCoursesMarks));
		}
		return combinedStudentExamMarksDetails;
	}

	/**
	 * Maps the child course dimensions to parent course dimensions
	 *
	 * @param studentExamMarksDetailsList
	 * @param parentExamDetails
	 * @return
	 */
	private List<StudentExamMarksDetails> mapParentDimensions(int instituteId,
			final List<StudentExamMarksDetails> studentExamMarksDetailsList, ExamDetails parentExamDetails,
			boolean scaleToParentMaxMarks, StandardMetadata standardMetadata) {
		//TODO : Attempting Rounding Solution
//		if(instituteId == 10120){
		if(standardMetadata == null || standardMetadata.isRoundExamReportMarks()){
			for(StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList){
				for(ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesMarks()){
					for(ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()){
						if(examDimensionObtainedValues.getObtainedMarks() != null){
							examDimensionObtainedValues.setObtainedMarks(Math.round(examDimensionObtainedValues.getObtainedMarks()) * 1d);
						}
					}
				}
			}
		}

		if (parentExamDetails == null) {
			return studentExamMarksDetailsList;
		}

		final List<StudentExamMarksDetails> mappedStudentExamMarksDetails = new ArrayList<>();

		final Map<UUID, Map<Integer, ExamDimensionValues>> parentCourseExamDimensionValues = getCourseDimensionMapping(
				parentExamDetails.getExamCoursesAllDimensions());

		for (final StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			final List<ExamCourseMarks> updatedExamCoursesMarksList = new ArrayList<>();

			for (final ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {
				final UUID courseId = examCourseMarks.getCourse().getCourseId();

				/**
				 * Skip if parent exam does not contain course of child exam
				 */
				if (!parentCourseExamDimensionValues.containsKey(courseId)) {
					continue;
				}

				final Map<Integer, ExamDimensionObtainedValues> examDimensionObtainedValuesMap = new HashMap<>();

				for (final ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
						.getExamDimensionObtainedValues()) {
					examDimensionObtainedValuesMap.put(examDimensionObtainedValues.getExamDimension().getDimensionId(),
							examDimensionObtainedValues);
				}

				final List<ExamDimensionObtainedValues> examDimensionObtainedValuesList = new ArrayList<>();
				for (final ExamDimensionValues examDimensionValues : parentCourseExamDimensionValues.get(courseId)
						.values()) {
					final Integer dimensionId = examDimensionValues.getExamDimension().getDimensionId();

					/**
					 * Only mapping dimensions one to one which are present in
					 * parent exam
					 */
					if (!examDimensionObtainedValuesMap.containsKey(dimensionId)) {
						continue;
					}

					final ExamDimensionObtainedValues examDimensionObtainedValues = examDimensionObtainedValuesMap
							.get(dimensionId);
					if (examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER) {

						final Double parentMaxMarks = examDimensionValues.getOriginalMaxMarks();
						final Double parentMinMarks = examDimensionValues.getMinMarks();
						if (parentMaxMarks == null || Double.compare(parentMaxMarks, 0) <= 0) {
							logger.warn(
									"Parent exam course {} max marks are not set in dimension {}. Skipping dimension",
									courseId, dimensionId);
							continue;
						}

						if (scaleToParentMaxMarks) {
							Double scaledMarks = null;
							Double scaledMaxMarks = null;
							if (examDimensionObtainedValues.getObtainedMarks() != null) {
								scaledMarks = examDimensionObtainedValues.getObtainedMarks() * parentMaxMarks
										/ examDimensionObtainedValues.getOriginalMaxMarks();
							}

							if (examDimensionObtainedValues.getMaxMarks() != null) {
								scaledMaxMarks = examDimensionObtainedValues.getMaxMarks() * parentMaxMarks
										/ examDimensionObtainedValues.getOriginalMaxMarks();
							}
							/**
							 * TODO: Not scaling grace marks. Check if needed
							 */
//							if(instituteId == 10120){
							if(standardMetadata == null || standardMetadata.isRoundExamReportMarks()){
								scaledMaxMarks = scaledMaxMarks == null ? null : Math.round(scaledMaxMarks)*1d;
								scaledMarks = scaledMarks == null ? null : Math.round(scaledMarks)*1d;
							}
							examDimensionObtainedValues.setMaxMarks(parentMaxMarks, scaledMaxMarks);
							examDimensionObtainedValues.setMinMarks(parentMinMarks);
							examDimensionObtainedValues.setObtainedMarks(scaledMarks);
						}

					}
					examDimensionObtainedValuesList.add(examDimensionObtainedValues);

				}
				updatedExamCoursesMarksList
						.add(new ExamCourseMarks(examCourseMarks.getCourse(), examDimensionObtainedValuesList));
			}
			mappedStudentExamMarksDetails.add(new StudentExamMarksDetails(studentExamMarksDetails.getStudent(),
					studentExamMarksDetails.getExamMetaData(), parentExamDetails.getExamDimensionValues(),
					updatedExamCoursesMarksList));
		}
		return mappedStudentExamMarksDetails;

	}

	private Map<UUID, Map<Integer, ExamDimensionValues>> getCourseDimensionMapping(
			List<ExamCourse> parentExamCoursesAllDimensions) {
		final Map<UUID, Map<Integer, ExamDimensionValues>> courseExamDimensionValues = new HashMap<>();
		for (final ExamCourse examCourse : parentExamCoursesAllDimensions) {
			if (!courseExamDimensionValues.containsKey(examCourse.getCourse().getCourseId())) {
				courseExamDimensionValues.put(examCourse.getCourse().getCourseId(), new HashMap<>());
			}
			for (final ExamDimensionValues examDimensionValues : examCourse.getExamDimensionValues()) {
				courseExamDimensionValues.get(examCourse.getCourse().getCourseId())
						.put(examDimensionValues.getExamDimension().getDimensionId(), examDimensionValues);
			}
		}
		return courseExamDimensionValues;
	}

	public List<CourseMarksFeedExamWithDimensions> getClassMarksFeedStructureWithDimensions(UUID standardId, int instituteId,
																							int academicSessionId) {

		List<CourseMarksFeedExams> courseMarksFeedExamsList = getClassMarksFeedStructure(standardId, instituteId, academicSessionId);

		StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);

		final List<ExamCoursesData> examCoursesDataList = examinationDao.getExamCourses(academicSessionId, standardId);
		Map<UUID, ExamCoursesData> examCoursesDataMap = getExamCoursesDataMap(examCoursesDataList);

		List<CourseMarksFeedExamWithDimensions> courseMarksFeedExamWithDimensionsList = new ArrayList<>();
		if (CollectionUtils.isEmpty(courseMarksFeedExamsList) || examCoursesDataMap == null
				|| CollectionUtils.isEmpty(examCoursesDataMap.entrySet())) {
			return courseMarksFeedExamWithDimensionsList;
		}

		boolean isScholasticGradingEnabled = standardMetaData != null && standardMetaData.isScholasticGradingEnabled();
		boolean isCoscholasticGradingEnabled = standardMetaData != null && standardMetaData.isCoScholasticGradingEnabled();
		for(CourseMarksFeedExams courseMarksFeedExams : courseMarksFeedExamsList) {
			List<ExamCoursesData> examCoursesDatas = new ArrayList<>();
			for(ExamMetaData examMetaData : courseMarksFeedExams.getExamMetaDatas()) {
				if(examMetaData == null || examMetaData.getExamId() == null) {
					continue;
				}
				UUID examId = examMetaData.getExamId();
				ExamCoursesData examCoursesData = examCoursesDataMap.get(examId);
				if(examCoursesData == null) {
					continue;
				}
				examCoursesDatas.add(examCoursesData);
			}
			CourseMarksFeedExamWithDimensions courseMarksFeedExamWithDimensions = new CourseMarksFeedExamWithDimensions(
					courseMarksFeedExams.getCourse(), isCoscholasticGradingEnabled, isScholasticGradingEnabled, examCoursesDatas);
			courseMarksFeedExamWithDimensionsList.add(courseMarksFeedExamWithDimensions);
		}

		return sortCourseMarksFeedExamWithDimensionsBySequence(courseMarksFeedExamWithDimensionsList);
	}
	public List<CourseMarksFeedExamWithDimensionsFilter> getClassMarksFeedStructureWithDimensionsFilter(UUID standardId, int instituteId,
																							int academicSessionId) {

		List<CourseMarksFeedExams> courseMarksFeedExamsList = getClassMarksFeedStructure(standardId, instituteId, academicSessionId);

		StandardMetadata standardMetadata = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
		
		Map<UUID,Set<UUID>> examIdset = new HashMap<>();
		for(CourseMarksFeedExams courseMarksFeedExams : courseMarksFeedExamsList){
			UUID courseId = courseMarksFeedExams.getCourse().getCourseId();
			examIdset.putIfAbsent(courseId, new HashSet<>());
			for(ExamMetaData examId : courseMarksFeedExams.getExamMetaDatas()){
				examIdset.get(courseId).add(examId.getExamId());
			}
		}
		final List<ExamCoursesData> examCoursesDataList = examinationDao.getExamCourses(academicSessionId, standardId);
		Map<UUID, List<ExamDimensionCoursesData>> examCoursesDimensionDataMap = getExamCoursesDimensionDataMap(examCoursesDataList,examIdset);

		List<CourseMarksFeedExamWithDimensionsFilter> courseMarksFeedExamWithDimensionsFilterList = new ArrayList<>();
		if (CollectionUtils.isEmpty(courseMarksFeedExamsList) || examCoursesDimensionDataMap == null
				|| CollectionUtils.isEmpty(examCoursesDimensionDataMap.entrySet())) {
			return courseMarksFeedExamWithDimensionsFilterList;
		}

		boolean isScholasticGradingEnabled = standardMetadata != null && standardMetadata.isScholasticGradingEnabled();
		boolean isCoscholasticGradingEnabled = standardMetadata != null && standardMetadata.isCoScholasticGradingEnabled();
		for(CourseMarksFeedExams courseMarksFeedExams : courseMarksFeedExamsList) {
			UUID courseId = courseMarksFeedExams.getCourse().getCourseId();
			List<ExamDimensionCoursesData> examCoursesDimensionDatas = examCoursesDimensionDataMap.get(courseId);
			if(CollectionUtils.isEmpty(examCoursesDimensionDatas)) {
					continue;
			}

			CourseMarksFeedExamWithDimensionsFilter courseMarksFeedExamWithDimensions = new CourseMarksFeedExamWithDimensionsFilter(
					courseMarksFeedExams.getCourse(), isCoscholasticGradingEnabled, isScholasticGradingEnabled, examCoursesDimensionDatas);
					courseMarksFeedExamWithDimensionsFilterList.add(courseMarksFeedExamWithDimensions);
		}

		return sortCourseMarksFeedExamWithDimensionsFilterBySequence(courseMarksFeedExamWithDimensionsFilterList);
	}

	public List<CourseExamDimensionDetails> getCourseExamDimensionDetails(UUID standardId, int instituteId,
																							int academicSessionId) {

		StopWatch stopWatch = new StopWatch();

		stopWatch.start();
		List<CourseMarksFeedExams> courseMarksFeedExamsList = getClassMarksFeedStructure(standardId, instituteId, academicSessionId);
		stopWatch.stop();
		logger.info("courseMarksFeedExamsList time = {}", stopWatch.getLastTaskTimeMillis());

		stopWatch.start();
		StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
		stopWatch.stop();
		logger.info("standardMEtadata time = {}", stopWatch.getLastTaskTimeMillis());

		stopWatch.start();
		final List<ExamCoursesData> examCoursesDataList = examinationDao.getExamCourses(academicSessionId, standardId);
		stopWatch.stop();
		logger.info("examCoursesData list time = {}", stopWatch.getLastTaskTimeMillis());

		stopWatch.start();
		Map<UUID, ExamCoursesData> examCoursesDataMap = getExamCoursesDataMap(examCoursesDataList);
		stopWatch.stop();
		logger.info("examCoursesDataMap time = {}", stopWatch.getLastTaskTimeMillis());

		stopWatch.start();
		List<CourseExamDimensionDetails> courseExamDimensionDetailsList = new ArrayList<>();
		if (CollectionUtils.isEmpty(courseMarksFeedExamsList) || examCoursesDataMap == null
				|| CollectionUtils.isEmpty(examCoursesDataMap.entrySet())) {
			return courseExamDimensionDetailsList;
		}
		stopWatch.stop();
		logger.info("courseExamDimensionDetailsList time = {}", stopWatch.getLastTaskTimeMillis());

		stopWatch.start();
		boolean isScholasticGradingEnabled = standardMetaData != null && standardMetaData.isScholasticGradingEnabled();
		boolean isCoscholasticGradingEnabled = standardMetaData != null && standardMetaData.isCoScholasticGradingEnabled();
		stopWatch.stop();
		logger.info("fetching isScholasticGradingEnabled & isCoscholasticGradingEnabledtime = {}", stopWatch.getLastTaskTimeMillis());

		stopWatch.start();
		for(CourseMarksFeedExams courseMarksFeedExams : courseMarksFeedExamsList) {
			List<ExamDimensionData> examDimensionDataList = new ArrayList<>();
			UUID courseId = courseMarksFeedExams.getCourse().getCourseId();
			for(ExamMetaData examMetaData : courseMarksFeedExams.getExamMetaDatas()) {
				if(examMetaData == null || examMetaData.getExamId() == null) {
					continue;
				}
				UUID examId = examMetaData.getExamId();
				ExamCoursesData examCoursesData = examCoursesDataMap.get(examId);
				if(examCoursesData == null) {
					continue;
				}
				ExamMetaData examMetaData1 = examCoursesData.getExamMetaData();
				ExamCoursePublishedStatus examCoursePublishedStatus = examCoursesData.getExamCoursePublishedStatus();
				List<ExamDimension> examDimensionList = new ArrayList<>();
				for(ExamCourse examCourse : examCoursesData.getExamCourses()) {
					if(!examCourse.getCourse().getCourseId().equals(courseId)) {
						continue;
					}
					for(ExamDimensionValues examDimensionValues : examCourse.getExamDimensionValues()) {
						if(!examDimensionValues.getExamDimension().isTotal() && examDimensionValues.getMaxMarks() == null) {
							continue;
						}
						examDimensionList.add(examDimensionValues.getExamDimension());
					}
					break;
				}
				examDimensionDataList.add(new ExamDimensionData(examMetaData1, examDimensionList, examCoursePublishedStatus));
			}
			CourseExamDimensionDetails courseExamDimensionDetails = new CourseExamDimensionDetails(
					courseMarksFeedExams.getCourse(), isCoscholasticGradingEnabled, isScholasticGradingEnabled, examDimensionDataList);
			courseExamDimensionDetailsList.add(courseExamDimensionDetails);
		}

		stopWatch.stop();
		logger.info("for loop time = {}", stopWatch.getLastTaskTimeMillis());

		CourseExamDimensionDetails.sortCourseExamDimensionDetailsBySequence(courseExamDimensionDetailsList);

		return courseExamDimensionDetailsList;
	}
	public static List<CourseMarksFeedExamWithDimensions> sortCourseMarksFeedExamWithDimensionsBySequence(List<CourseMarksFeedExamWithDimensions> examCourseList) {

		if(org.springframework.util.CollectionUtils.isEmpty(examCourseList)) {
			return new ArrayList<>();
		}
		Collections.sort(examCourseList, new Comparator<CourseMarksFeedExamWithDimensions>() {
			@Override
			public int compare(CourseMarksFeedExamWithDimensions e1, CourseMarksFeedExamWithDimensions e2) {
				return e1.getCourse().compareTo(e2.getCourse());
			}
		});
		return examCourseList;
	}
	public static List<CourseMarksFeedExamWithDimensionsFilter> sortCourseMarksFeedExamWithDimensionsFilterBySequence(List<CourseMarksFeedExamWithDimensionsFilter> examCourseList) {

		if(org.springframework.util.CollectionUtils.isEmpty(examCourseList)) {
			return new ArrayList<>();
		}
		Collections.sort(examCourseList, new Comparator<CourseMarksFeedExamWithDimensionsFilter>() {
			@Override
			public int compare(CourseMarksFeedExamWithDimensionsFilter e1, CourseMarksFeedExamWithDimensionsFilter e2) {
				return e1.getCourse().compareTo(e2.getCourse());
			}
		});
		return examCourseList;
	}

	private Map<UUID, ExamCoursesData> getExamCoursesDataMap(List<ExamCoursesData> examCoursesDataList) {
		if(CollectionUtils.isEmpty(examCoursesDataList)) {
			return null;
		}
		Map<UUID, ExamCoursesData> examCoursesDataMap = new HashMap<>();
		for(ExamCoursesData examCoursesData : examCoursesDataList) {
			if(!examCoursesDataMap.containsKey(examCoursesData.getExamMetaData().getExamId())) {
				examCoursesDataMap.put(examCoursesData.getExamMetaData().getExamId(), examCoursesData);
			}
		}
		return examCoursesDataMap;
	}
	private Map<UUID, List<ExamDimensionCoursesData>> getExamCoursesDimensionDataMap(List<ExamCoursesData> examCoursesDataList,Map<UUID,Set<UUID>> examIdSet) {
		if(CollectionUtils.isEmpty(examCoursesDataList)) {
			return null;
		}
		Map<UUID, List<ExamDimensionCoursesData>> examDimensionCoursesDataMap = new HashMap<>();
		for(ExamCoursesData examCoursesData : examCoursesDataList) {
			UUID examId = examCoursesData.getExamMetaData().getExamId();
			if (examId == null) {
				continue;
			}
			for (ExamCourse examCourse : examCoursesData.getExamCourses()) {
				UUID courseId = examCourse.getCourse().getCourseId();
				examDimensionCoursesDataMap.putIfAbsent(courseId, new ArrayList<>());
				if(!examIdSet.get(courseId).contains(examId))
				{
					continue;
				}
				ExamDimensionCoursesData examDimensionCourseData = new ExamDimensionCoursesData(
                examCoursesData.getExamMetaData(),
                examCourse.getExamDimensionValues()
            	);
				examDimensionCoursesDataMap.get(courseId).add(examDimensionCourseData);
			}
		}
		return examDimensionCoursesDataMap;
	}

	public List<CourseMarksFeedExams> getClassMarksFeedStructure(UUID standardId, int instituteId,
			int academicSessionId) {
		final List<ExamNodeData> examNodeDatas = examinationDao.getExamGraphNodesByStandard(standardId,
				academicSessionId, instituteId, true);
		final List<CourseMarksFeedExams> courseMarksFeedExams = new ArrayList<>();
		if (CollectionUtils.isEmpty(examNodeDatas)) {
			return courseMarksFeedExams;
		}
		final Set<UUID> rootNodes = new HashSet<>();
		final Map<UUID, ExamNodeData> examMetaDataMap = new HashMap<>();
		final Map<UUID, Course> courseMap = new HashMap<>();

		for (final ExamNodeData examNodeData : examNodeDatas) {
			final UUID examId = examNodeData.getExamMetaData().getExamId();
			examMetaDataMap.put(examId, examNodeData);
			if (!CollectionUtils.isEmpty(examNodeData.getCourses())) {
				for (final Course course : examNodeData.getCourses()) {
					courseMap.put(course.getCourseId(), course);
				}
			}
			if (CollectionUtils.isEmpty(examNodeData.getExamMetaData().getParentIds())) {
				rootNodes.add(examId);
			}
		}

		if (CollectionUtils.isEmpty(rootNodes)) {
			logger.error("Exams are in inconsistent state. No root node found.");
			return null;
		}

		final Map<UUID, Set<UUID>> markableExams = new HashMap<>();
		for (final UUID rootId : rootNodes) {
			final ExamNode rootNode = new ExamNode(examMetaDataMap.get(rootId).getExamMetaData(),
					examMetaDataMap.get(rootId).getCourses());
			final Map<UUID, Set<UUID>> courseMarksFeedingExams = getCourseMarksFeedingExams(rootNode, examMetaDataMap);
			for (final Entry<UUID, Set<UUID>> marksFeedingExamEntry : courseMarksFeedingExams.entrySet()) {
				final UUID courseId = marksFeedingExamEntry.getKey();
				if (!markableExams.containsKey(courseId)) {
					markableExams.put(courseId, new HashSet<>());
				}
				markableExams.get(courseId).addAll(marksFeedingExamEntry.getValue());
			}

		}

		for (final Entry<UUID, Course> courseEntry : courseMap.entrySet()) {
			final UUID courseId = courseEntry.getKey();
			if (CollectionUtils.isEmpty(markableExams.get(courseId))) {
				courseMarksFeedExams.add(new CourseMarksFeedExams(courseEntry.getValue(), new ArrayList<>()));
				continue;
			}
			List<ExamMetaData> examMetaDatas = new ArrayList<>();
			for (final UUID examId : markableExams.get(courseId)) {
				examMetaDatas.add(examMetaDataMap.get(examId).getExamMetaData());
			}
			examMetaDatas = ExamMetaData.sortExamByName(examMetaDatas);
			courseMarksFeedExams.add(new CourseMarksFeedExams(courseEntry.getValue(), examMetaDatas));
		}
		return sortCourseMarksFeedExamsBySequence(courseMarksFeedExams);
	}
	public static List<CourseMarksFeedExams> sortCourseMarksFeedExamsBySequence(List<CourseMarksFeedExams> examCourseList) {
		if(org.springframework.util.CollectionUtils.isEmpty(examCourseList)) {
			return new ArrayList<>();
		}
		Collections.sort(examCourseList, new Comparator<CourseMarksFeedExams>() {
			@Override
			public int compare(CourseMarksFeedExams e1, CourseMarksFeedExams e2) {
				return e1.getCourse().compareTo(e2.getCourse());
			}
		});
		return examCourseList;
	}
	private Map<UUID, Set<UUID>> getCourseMarksFeedingExams(ExamNode examNode,
			final Map<UUID, ExamNodeData> examMetaDataMap) {
		final UUID examId = examNode.getExamMetaData().getExamId();
		final Map<UUID, Set<UUID>> courseMarksFeedingExams = new HashMap<>();
		final Set<UUID> childIds = examMetaDataMap.get(examId).getChildIds();
		if (CollectionUtils.isEmpty(childIds)) {
			if (CollectionUtils.isEmpty(examNode.getCourses())) {
				return courseMarksFeedingExams;
			}
			for (final Course course : examNode.getCourses()) {
				courseMarksFeedingExams.put(course.getCourseId(), new HashSet<>(Arrays.asList(examId)));
			}
			return courseMarksFeedingExams;
		}

		for (final UUID childId : childIds) {
			final Map<UUID, Set<UUID>> childMarksFeedingExams = getCourseMarksFeedingExams(
					new ExamNode(examMetaDataMap.get(childId).getExamMetaData(),
							examMetaDataMap.get(childId).getCourses()),
					examMetaDataMap);
			for (final Entry<UUID, Set<UUID>> childMarksFeedingExamEntry : childMarksFeedingExams.entrySet()) {
				final UUID courseId = childMarksFeedingExamEntry.getKey();
				if (!courseMarksFeedingExams.containsKey(courseId)) {
					courseMarksFeedingExams.put(courseId, new HashSet<>());
				}
				courseMarksFeedingExams.get(courseId).addAll(childMarksFeedingExamEntry.getValue());
			}
		}
		for (final Course course : examNode.getCourses()) {
			final UUID courseId = course.getCourseId();
			if (!courseMarksFeedingExams.containsKey(courseId)) {
				courseMarksFeedingExams.put(course.getCourseId(), new HashSet<>(Arrays.asList(examId)));
			}
		}
		return courseMarksFeedingExams;
	}

	/**
	 * This method feeds marks for single exam
	 *
	 * @param marksFeedDatas
	 * @return
	 */
	public boolean feedMarks(int instituteId, int academicSessionId, List<MarksFeedData> marksFeedDatas, MarksFeedStatus marksFeedStatus,
			UUID standardId, Integer sectionId, UUID userId) {


		if(instituteId <= 0) {
			logger.error("Invalid institute id");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute Id"));
		}

		if(marksFeedStatus == null) {
			logger.error("Invalid marksFeedStatus");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid marksFeedStatus"));
		}

		if(CollectionUtils.isEmpty(marksFeedDatas)) {
			logger.error("Invalid payload");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid payload"));
		}

		/**
		 * Assuming students will belong to a single class when marks is filled from MOBILE or WEB
		 */
		if(academicSessionId <= 0 || standardId == null) {
			ExamDetails examDetails = getExamDetails(marksFeedDatas.get(0).getExamId(), instituteId);
			if(academicSessionId <= 0) {
				academicSessionId = examDetails.getAcademicSession().getAcademicSessionId();
			}
			if(standardId == null) {
				standardId = examDetails.getStandard().getStandardId();
			}
		}

		Set<UUID> courseIdSet = new HashSet<>();
		Set<Integer> dimensionIdSet = new HashSet<>();
		for(MarksFeedData marksFeedData : marksFeedDatas) {
			courseIdSet.add(marksFeedData.getCourseId());
			for(ExamDimensionObtainedValues examDimensionObtainedValues : marksFeedData.getExamDimensionObtainedValues()){
				dimensionIdSet.add(examDimensionObtainedValues.getExamDimension().getDimensionId());
			}
		}

		for(UUID courseId : courseIdSet) {
			/**
			 * only faculty with have entity assignment or user with special access can add update marks for that standard+section+course
			 */
			verifyExaminationDataAccessibility(instituteId, academicSessionId, standardId, sectionId, courseId, userId, true);
		}
		
		final UserPermissions userPermissions = userPermissionManager.getUserPermissions(instituteId, userId);
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_MARKS,
				userPermissions);

		final Pair<UUID, Set<UUID>> examCoursesPair = validateMarksFeedPayload(marksFeedDatas);
		final UUID examId = examCoursesPair.getFirst();
		final boolean marksSubmitted = marksSubmitted(instituteId, examId, examCoursesPair.getSecond(), dimensionIdSet, sectionId);

		if (marksSubmitted) {
			final boolean authorizedToUpdateSubmittedMarks = userPermissionManager.isAuthorisedAction(instituteId,
					userId, AuthorisationRequiredAction.UPDATE_SUBMITTED_MARKS, userPermissions);

			if (!authorizedToUpdateSubmittedMarks) {
				logger.error("Marks are already submitted for exam {},  courses {}. Skipping any update.", examId,
						examCoursesPair.getSecond());
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
						"Marks are already submitted and cannot be changed."));
			}

			if (marksFeedStatus != MarksFeedStatus.SUBMITTED) {
				logger.error(
						"Marks are already submitted for exam {},  courses {}. Authorized person cant change status to  {}.",
						examId, examCoursesPair.getSecond(), marksFeedStatus);
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
						"Marks are already submitted. Cant change status apart from SUBMITTED."));
			}
		}

		final ExamDetails examDetails = getExamDetails(examId, instituteId);
		final Map<UUID, Course> examCourseMap = new HashMap<>();

		if (!CollectionUtils.isEmpty(examDetails.getExamCourses())) {
			for (final ExamCourse examCourse : examDetails.getExamCourses()) {
				examCourseMap.put(examCourse.getCourse().getCourseId(), examCourse.getCourse());
			}
		}

		final Map<CourseType, List<ExamGrade>> examGradesMap = getExamGrades(instituteId,
				examDetails.getAcademicSession().getAcademicSessionId(), examDetails.getStandard().getStandardId());

		final Map<UUID, Map<Integer, ExamDimensionValues>> courseDimensionValues = getCourseDimensionValues(
				examDetails);
		if (!validateFeededMarks(examId, courseDimensionValues, marksFeedDatas, examCourseMap, examGradesMap)) {
			logger.error("Validation failed for entered marks for exam {}", examDetails.getExamMetaData().getExamId());
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Validation failed for entered marks for exam "));
		}

		filterFeedMarksDimensions(marksFeedDatas, examDetails, courseDimensionValues);

		return examinationDao.feedMarks(getFilteredCourseStudentMarks(marksFeedDatas), marksFeedStatus);
	}

	private boolean verifyExaminationDataAccessibility(int instituteId, int academicSessionId, UUID standardId, Integer sectionId, UUID courseId, UUID userId, boolean throwError) {
		if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.ACCESS_ALL_CLASSES_EXAMINATION_MARKS_DATA, false)) {
			Map<String, Set<UUID>> staffStandardSectionStrCourseDetailsMap = timetableManager.getStaffStandardSectionIdEntityDetails(
					instituteId, academicSessionId, userId);
			if(!EPermissionUtils.checkUserAccessibility(staffStandardSectionStrCourseDetailsMap, standardId, new HashSet<>(Collections.singletonList(sectionId)), courseId)) {
				if(throwError) {
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
							"You are not allowed to access/update this class and course exam marks data."));
				}
				return false;
			}
		}
		return true;
	}

	private List<MarksFeedData> getFilteredCourseStudentMarks(List<MarksFeedData> marksFeedDataList) {
		final Set<UUID> courseIds = new HashSet<>();
		for (final MarksFeedData marksFeedData : marksFeedDataList) {
			courseIds.add(marksFeedData.getCourseId());
		}
		if (CollectionUtils.isEmpty(courseIds)) {
			return new ArrayList<>();
		}
		final Map<UUID, CourseStudents> courseStudentsMap = new HashMap<>();
		for (final UUID courseId : courseIds) {
			final CourseStudents courseStudents = courseManager.getStudentsByCourse(courseId);
			courseStudentsMap.put(courseId, courseStudents);
		}

		final List<MarksFeedData> filteredMarksFeedDataList = new ArrayList<>();

		for (final MarksFeedData marksFeedData : marksFeedDataList) {
			final UUID courseId = marksFeedData.getCourseId();
			final CourseStudents courseStudents = courseStudentsMap.get(courseId);
			if (courseStudents == null || courseStudents.getCourse().isMandatory()) {
				filteredMarksFeedDataList.add(marksFeedData);
				continue;
			}

			if (!CollectionUtils.isEmpty(courseStudents.getStudents())
					&& courseStudents.getStudents().contains(marksFeedData.getStudentId())) {
				filteredMarksFeedDataList.add(marksFeedData);
			}
		}
		return filteredMarksFeedDataList;
	}

	/**
	 * Filtering out all dimensions which are not part of exam as per
	 * configurations
	 *
	 * @param marksFeedDatas
	 * @param examDetails
	 * @param courseDimensionValues
	 */
	private void filterFeedMarksDimensions(List<MarksFeedData> marksFeedDatas, final ExamDetails examDetails,
			final Map<UUID, Map<Integer, ExamDimensionValues>> courseDimensionValues) {

		for (final MarksFeedData marksFeedData : marksFeedDatas) {
			if (!courseDimensionValues.containsKey(marksFeedData.getCourseId())) {
				logger.error("Course {} does not belong to exam {} ", marksFeedData.getCourseId(),
						examDetails.getExamMetaData().getExamId());
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
						"Course given in payload does not belong to exam "));
			}

			final Iterator<ExamDimensionObtainedValues> examDimensionObtainedValuesIterator = marksFeedData
					.getExamDimensionObtainedValues().iterator();
			while (examDimensionObtainedValuesIterator.hasNext()) {
				final ExamDimensionObtainedValues examDimensionObtainedValues = examDimensionObtainedValuesIterator
						.next();
				if (!courseDimensionValues.get(marksFeedData.getCourseId())
						.containsKey(examDimensionObtainedValues.getExamDimension().getDimensionId())) {
					examDimensionObtainedValuesIterator.remove();
				} else {
					final ExamDimensionValues examDimensionValues = courseDimensionValues
							.get(marksFeedData.getCourseId())
							.get(examDimensionObtainedValues.getExamDimension().getDimensionId());

					/**
					 * Removing additional invalid fields
					 */
					if (examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER) {
						examDimensionObtainedValues.setObtainedGrade(null);
					} else if (examDimensionValues.getExamDimension()
							.getExamEvaluationType() == ExamEvaluationType.GRADE) {
						examDimensionObtainedValues.setObtainedMarks(null);
						examDimensionObtainedValues.setGraceMarks(null);
					}
				}
			}

		}
	}

	/**
	 * Checks across all the courses provided in exam whether marks in any
	 * dimension is submitted
	 *
	 * @param instituteId
	 * @param examId
	 * @param courseIds
	 * @return
	 */
	private boolean marksSubmitted(int instituteId, UUID examId, Set<UUID> courseIds, Set<Integer> dimensionIds, Integer sectionId) {
		final List<StudentExamMarksDetails> existingStudentExamMarksDetails = getClassMarks(instituteId, examId,
				sectionId, false);
		for (final StudentExamMarksDetails studentExamMarksDetails : existingStudentExamMarksDetails) {
			if (CollectionUtils.isEmpty(studentExamMarksDetails.getExamCoursesMarks())) {
				continue;
			}

			for (final ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesMarks()) {
				if (!courseIds.contains(examCourseMarks.getCourse().getCourseId())
						|| CollectionUtils.isEmpty(examCourseMarks.getExamDimensionObtainedValues())) {
					continue;
				}

				for (final ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
						.getExamDimensionObtainedValues()) {
					if ( dimensionIds.contains(examDimensionObtainedValues.getExamDimension().getDimensionId()) && examDimensionObtainedValues.getMarksFeedStatus() == MarksFeedStatus.SUBMITTED) {
						return true;
					}
				}
			}
		}
		return false;
	}

	private Map<UUID, Map<Integer, ExamDimensionValues>> getCourseDimensionValues(ExamDetails examDetails) {
		final Map<UUID, Map<Integer, ExamDimensionValues>> courseDimensionValues = new HashMap<>();
		for (final ExamCourse examCourse : examDetails.getExamCoursesAllDimensions()) {
			final UUID courseId = examCourse.getCourse().getCourseId();
			if (!courseDimensionValues.containsKey(courseId)) {
				courseDimensionValues.put(courseId, new HashMap<>());
			}
			for (final ExamDimensionValues examDimensionValues : examCourse.getExamDimensionValues()) {
				courseDimensionValues.get(courseId).put(examDimensionValues.getExamDimension().getDimensionId(),
						examDimensionValues);
			}
		}
		return courseDimensionValues;
	}

	private boolean validateFeededMarks(UUID examId, Map<UUID, Map<Integer, ExamDimensionValues>> courseDimensionValues,
			List<MarksFeedData> marksFeedDatas, Map<UUID, Course> examCourseMap,
			Map<CourseType, List<ExamGrade>> examGradesMap) {
		for (final MarksFeedData marksFeedData : marksFeedDatas) {
			if (!examCourseMap.containsKey(marksFeedData.getCourseId())) {
				logger.error("Course not configured for exam {}, course {}", examId, marksFeedData.getCourseId());
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
						"Course not configured for exam"));
			}
			final CourseType courseType = examCourseMap.get(marksFeedData.getCourseId()).getCourseType();

			final Map<Integer, ExamDimensionValues> examDimensionValueMap = courseDimensionValues
					.get(marksFeedData.getCourseId());
			if (MapUtils.isEmpty(examDimensionValueMap)) {
				return false;
			}
			for (final ExamDimensionObtainedValues examDimensionObtainedValues : marksFeedData
					.getExamDimensionObtainedValues()) {
				final Integer dimensionId = examDimensionObtainedValues.getExamDimension().getDimensionId();
				final ExamDimensionValues examDimensionValues = examDimensionValueMap.get(dimensionId);
				if (examDimensionValues == null) {
					logger.error("Dimension not configured for exam {}, course {}, dimension {}", examId,
							marksFeedData.getCourseId(), dimensionId);
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
							"Dimension not configured for exam"));
				}
				final Double obtainedMarks = examDimensionObtainedValues.getObtainedMarks();
				final ExamGrade obtainedGrade = examDimensionObtainedValues.getObtainedGrade();
				final ExamAttendanceStatus attendanceStatus = examDimensionObtainedValues.getAttendanceStatus();
				if (obtainedMarks == null && ExamGrade.emptyGrade(obtainedGrade) && attendanceStatus == null) {
					continue;
				} else if (obtainedMarks != null) {
					if (examDimensionValues.getMaxMarks() == null) {
						logger.error("Max marks not configured for exam {}, course {}, dimension {}", examId,
								marksFeedData.getCourseId(), dimensionId);
						throw new ApplicationException(new ErrorResponse(
								ApplicationErrorCode.INVALID_EXAM_CONFIGURATION, "Max marks not configured for exam"));
					}
					if (examDimensionValues.getMaxMarks() < obtainedMarks) {
						logger.error(
								"Obtained marks {} cannot be more then maximum marks {} configured for exam {}, course {}, dimension {}",
								obtainedMarks, examDimensionValues.getMaxMarks(), examId, marksFeedData.getCourseId(),
								dimensionId);
						throw new ApplicationException(
								new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
										"Obtained marks cannot be more then maximum marks configured for exam"));
					}
				} else if (obtainedGrade != null && obtainedGrade.getGradeId() != null && obtainedGrade.getGradeId() > 0) {
					if (!validExamGrade(obtainedGrade.getGradeId(), courseType, examGradesMap)) {
						throw new ApplicationException(
								new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
										"Invalid obtained grade. Not configured for exam"));
					}
				}
			}
		}
		return true;
	}

	private boolean validExamGrade(Integer gradeId, CourseType courseType,
			Map<CourseType, List<ExamGrade>> examGradesMap) {
		if (gradeId == null || gradeId <= 0) {
			return false;
		}
		if (!examGradesMap.containsKey(courseType)) {
			return false;
		}
		for (final ExamGrade examGrade : examGradesMap.get(courseType)) {
			if (examGrade.getGradeId().equals(gradeId)) {
				return true;
			}
		}
		return false;
	}

	private Pair<UUID, Set<UUID>> validateMarksFeedPayload(List<MarksFeedData> marksFeedDatas) {
		UUID examId = null;
		final Set<UUID> courseIds = new HashSet<>();
		for (final MarksFeedData marksFeedData : marksFeedDatas) {
			if (examId == null) {
				examId = marksFeedData.getExamId();
			}
			courseIds.add(marksFeedData.getCourseId());

			if (marksFeedData.getCourseId() == null || marksFeedData.getExamId() == null
					|| marksFeedData.getCourseId() == null
					|| CollectionUtils.isEmpty(marksFeedData.getExamDimensionObtainedValues())) {
				logger.error("Invalid marks feed payload");
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
						"Invalid marks feed payload"));
			}

			if (!examId.equals(marksFeedData.getExamId())) {
				logger.error("Marks does not belong to same exam");
				throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
						"Marks does not belong to same exam"));
			}

			for (final ExamDimensionObtainedValues examDimensionObtainedValues : marksFeedData
					.getExamDimensionObtainedValues()) {
				if (examDimensionObtainedValues.getObtainedMarks() != null
						&& !ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade())) {
					logger.error("Obtained marks and grade cannot be feed together.");
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
							"Obtained marks and grade cannot be feed together"));
				}

				if (examDimensionObtainedValues.getObtainedMarks() != null
						&& examDimensionObtainedValues.getAttendanceStatus() != null) {
					logger.error("Obtained marks and attendance status cannot be feed together.");
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
							"Obtained marks and attendance status cannot be feed together"));
				}

				if (!ExamGrade.emptyGrade(examDimensionObtainedValues.getObtainedGrade())
						&& examDimensionObtainedValues.getAttendanceStatus() != null) {
					logger.error("Obtained marks and attendance status cannot be feed together.");
					throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
							"Obtained marks and attendance status cannot be feed together"));
				}
			}
		}
		return new Pair<>(examId, courseIds);
	}

	public List<ExamCoursesData> getExamCourses(int instituteId, int academicSessionId, UUID standardId) {
		return getExamCourses(instituteId, academicSessionId, standardId, false, false);
	}

	public List<ExamCoursesData> getExamCourses(int instituteId, int academicSessionId, UUID standardId, boolean onlyLeafExams, boolean isFilteredDimension) {
		List<ExamCoursesData> examCoursesDataList = examinationDao.getExamCourses(academicSessionId, standardId);
		if (isFilteredDimension) {
			StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId, academicSessionId, standardId);
			for (final ExamCoursesData examCoursesData : examCoursesDataList) {
				List<ExamCourse> examCourseList = examCoursesData.getExamCourses();
				for (final ExamCourse examCourse : examCourseList) {
					filterDimensions(examCourse.getCourse().getCourseType(), examCourse.getExamDimensionValues(), standardMetaData);
				}
			}

		}
		if(!onlyLeafExams) {
			return ExamCoursesData.sortExamCoursesDataByExamStartDate(examCoursesDataList);
		}

		final Map<UUID, ExamNodeData> examNodeDataMap = getExamNodeDataMap(examinationDao.getExamGraphNodesByStandard(standardId,
				academicSessionId, instituteId, true));

		if(MapUtils.isEmpty(examNodeDataMap)) {
			return null;
		}

		List<ExamCoursesData> finalExamCoursesData = new ArrayList<ExamCoursesData>();
		for(ExamCoursesData examCoursesData : examCoursesDataList) {
			ExamNodeData examNodeData = examNodeDataMap.get(examCoursesData.getExamMetaData().getExamId());
			if(!CollectionUtils.isEmpty(examNodeData.getChildIds())) {
				continue;
			}
			finalExamCoursesData.add(examCoursesData);
		}

		return ExamCoursesData.sortExamCoursesDataByExamStartDate(finalExamCoursesData);
	}

	private Map<UUID, ExamNodeData> getExamNodeDataMap(List<ExamNodeData> examNodeDataList) {
		if(CollectionUtils.isEmpty(examNodeDataList)) {
			return null;
		}
		Map<UUID, ExamNodeData> examNodeDataMap = new HashMap<UUID, ExamNodeData>();
		for(ExamNodeData examNodeData : examNodeDataList) {
			if(!examNodeDataMap.containsKey(examNodeData.getExamMetaData().getExamId())) {
				examNodeDataMap.put(examNodeData.getExamMetaData().getExamId(), examNodeData);
			}
		}
		return examNodeDataMap;
	}

	// TODO : Need caching
	public List<ExamGrade> getExamGrades(int instituteId, int academicSessionId, UUID standardId,
			CourseType courseType) {
		final List<ExamGrade> examGrades = examinationDao.getExamGrades(instituteId, academicSessionId, standardId,
				courseType);
		if (CollectionUtils.isEmpty(examGrades)) {
			return new ArrayList<>();
		}

		Collections.sort(examGrades);

		return examGrades;
	}

	public Map<CourseType, List<StandardExaminationGrades>> getStandardExamGrades(int instituteId, int academicSessionId, String standardName) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}
		if(StringUtils.isBlank(standardName)){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Please Provide a standard name"));
		}

		StringBuilder incorrectStandardNames = new StringBuilder();
		List<UUID> standardIdsList = getStandardIdsByStandardName(instituteId, academicSessionId, Collections.singletonList(standardName), incorrectStandardNames);
		if (org.springframework.util.CollectionUtils.isEmpty(standardIdsList) || Collections.singletonList(standardName).size() != standardIdsList.size()) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_EXAMINATION_GRADES_DETAILS, "The list of standard name is incorrect " + incorrectStandardNames));
		}
		if(CollectionUtils.isEmpty(standardIdsList)){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Please Provide a valid standard name"));
		}
		// just taking first element beacuse this api support one standard at a time
		final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = getExamGrades(instituteId, academicSessionId, standardIdsList.get(0));
		if(courseTypeExamGrades == null || courseTypeExamGrades.isEmpty()){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Provided standard doesn't have any grading scheme"));
		}

		Map<CourseType, List<StandardExaminationGrades>> courseTypeExamGradeMap = new HashMap<>();

		for (Map.Entry<CourseType, List<ExamGrade>> entry : courseTypeExamGrades.entrySet()) {
			CourseType courseType = entry.getKey();
			List<ExamGrade> examGrades = entry.getValue();
			List<StandardExaminationGrades> standardExaminationGradesList = new ArrayList<>();
			for (ExamGrade grade : examGrades) {
				standardExaminationGradesList.add(new StandardExaminationGrades(0, grade.getGradeName(), grade.getGradeValue(), grade.getMarksRangeStart(), grade.getMarksRangeEnd(), grade.getRangeDisplayName(), grade.getRemarks(), grade.getCreditScore()));
			}
			courseTypeExamGradeMap.put(courseType, standardExaminationGradesList);
		}
		return courseTypeExamGradeMap;
	}

	// TODO : Need caching
	public Map<CourseType, List<ExamGrade>> getExamGrades(int instituteId, int academicSessionId, UUID standardId) {
		final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = examinationDao.getExamGrades(instituteId,
				academicSessionId, standardId);
		if (MapUtils.isEmpty(courseTypeExamGrades)) {
			return new HashMap<>();
		}
		for (final Entry<CourseType, List<ExamGrade>> entry : courseTypeExamGrades.entrySet()) {
			Collections.sort(entry.getValue());
		}

		return courseTypeExamGrades;
	}

	// TODO : Need caching
	public List<ExamGrade> getExamGrades(int instituteId, UUID examId, CourseType courseType) {

		final List<ExamGrade> examGrades = examinationDao.getExamGrades(instituteId, examId, courseType);
		if (CollectionUtils.isEmpty(examGrades)) {
			return new ArrayList<>();
		}

		Collections.sort(examGrades);

		return examGrades;

	}

	public List<ExamReportCardMetadata> getExamReportCardMetadatas(int instituteId, int academicSessionId,
			UUID standardId) {
		return examinationDao.getExamReportCardMetadatas(instituteId, academicSessionId, standardId);
	}

	public List<ExamReportCardBasicDetails> getExamReportCardBasicDetails(int instituteId, int academicSessionId) {
		List<ExamReportCardMetadata> examReportCardMetadataList = examinationDao.getExamReportCardMetadatas(instituteId, academicSessionId);
		if(CollectionUtils.isEmpty(examReportCardMetadataList)) {
			return null;
		}
		List<ExamReportCardBasicDetails> examReportCardBasicDetailsList = new ArrayList<>();
		List<String> reportCardTypeList = new ArrayList<>();
		for(ExamReportCardMetadata examReportCardMetadata : examReportCardMetadataList) {
			if(!reportCardTypeList.contains(examReportCardMetadata.getReportCardType())) {
				examReportCardBasicDetailsList.add(new ExamReportCardBasicDetails(
						examReportCardMetadata.getReportCardType(),
						examReportCardMetadata.getReportCardName()
				));
				reportCardTypeList.add(examReportCardMetadata.getReportCardType());
			}
		}
		return examReportCardBasicDetailsList;
	}

	public List<ClassExamMainStats> getInstituteTopStats(int instituteId, int academicSessionId) {
		final List<ClassExamMainStats> classExamMainStatsList = new ArrayList<>();
		final List<Standard> standards = instituteManager.getInstituteStandardDetails(instituteId, academicSessionId);
		for (final Standard standard : standards) {
			final List<ExamCoursesData> examCoursesDatas = getExamCourses(instituteId, academicSessionId, standard.getStandardId());
			ExamCoursesData rootExamCoursesData = null;
			for (final ExamCoursesData examCoursesData : examCoursesDatas) {
				if (CollectionUtils.isEmpty(examCoursesData.getExamMetaData().getParentIds())) {
					rootExamCoursesData = examCoursesData;
					break;
				}
			}
			if (rootExamCoursesData == null) {
				classExamMainStatsList.add(new ClassExamMainStats(standard));
				continue;
			}
			classExamMainStatsList.add(getClassExamMainStats(instituteId, academicSessionId,
					rootExamCoursesData.getExamMetaData().getExamId(), standard));
		}
		return classExamMainStatsList;
	}

	// TODO: Handle grades and total dimension
	private ClassExamMainStats getClassExamMainStats(int instituteId, int academicSessionId, UUID topExamId,
			Standard standard) {
		final List<StudentExamMarksDetails> studentExamMarksDetailsList = getResolvedClassMarks(instituteId, topExamId,
				null, null, false);

		if (CollectionUtils.isEmpty(studentExamMarksDetailsList)) {
			return new ClassExamMainStats(standard);
		}

		Double maxMarks = Double.MIN_VALUE;
		Double minMarks = Double.MAX_VALUE;
		Double totalMarks = 0d;
		for (final StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			Double totalExamMarks = 0d;
			Double totalExamMaxMarks = 0d;
			for (final ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {

				/**
				 * Skipping COSCHOLASTIC courses for stats computation
				 */
				if (examCourseMarks.getCourse().getCourseType() == CourseType.COSCHOLASTIC) {
					continue;
				}

				Double dimensionTotal = 0d;
				Double dimensionMaxTotal = 0d;

				for (final ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks
						.getExamDimensionObtainedValues()) {
					final ExamDimension examDimension = examDimensionObtainedValues.getExamDimension();
					/**
					 * Not counting courses for which max marks is not setup
					 */
					if (examDimension.getExamEvaluationType() == ExamEvaluationType.GRADE || examDimension.isTotal()
							|| examDimensionObtainedValues.getMaxMarks() == null) {
						continue;
					}

					if (examDimensionObtainedValues.getObtainedMarks() != null
							&& Double.compare(examDimensionObtainedValues.getObtainedMarks(), 0) >= 0) {
						dimensionTotal += examDimensionObtainedValues.getObtainedMarks();
					}

					dimensionMaxTotal += examDimensionObtainedValues.getMaxMarks();
				}
				totalExamMarks += dimensionTotal;
				totalExamMaxMarks += dimensionMaxTotal;
			}
			Double totalSum = 0d;
			if (Double.compare(totalExamMaxMarks, 0) > 0) {
				totalSum = totalExamMarks / totalExamMaxMarks;
			}
			if (totalSum > maxMarks) {
				maxMarks = totalSum;
			}
			if (totalSum < minMarks) {
				minMarks = totalSum;
			}
			totalMarks += totalSum;
		}

		return new ClassExamMainStats(standard, maxMarks, minMarks, totalMarks / studentExamMarksDetailsList.size());

	}

	private Map<CourseType, Map<Integer, Double>> getExamConfiguredMaxMarksMap(
			StudentExamMarksDetails studentExamMarksDetails) {
		final Map<CourseType, Map<Integer, Double>> examConfiguredMaxMarksMap = new HashMap<>();
		for (final Entry<CourseType, List<ExamDimensionValues>> entry : studentExamMarksDetails.getExamDimensionValues()
				.entrySet()) {
			if (!examConfiguredMaxMarksMap.containsKey(entry.getKey())) {
				examConfiguredMaxMarksMap.put(entry.getKey(), new HashMap<>());
			}
			for (final ExamDimensionValues examDimensionValues : entry.getValue()) {
				examConfiguredMaxMarksMap.get(entry.getKey()).put(
						examDimensionValues.getExamDimension().getDimensionId(), examDimensionValues.getMaxMarks());
			}
		}
		return examConfiguredMaxMarksMap;
	}

	public Boolean createDefaultExamForStandards(int instituteId, int academicSessionId) {

		if (instituteId <= 0 || academicSessionId <= 0) {
			logger.error("Invalid institute Id {} or invalid academic session Id {}", instituteId, academicSessionId);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid request"));
		}
		final List<UUID> standardIds = getStandardIds(instituteId, academicSessionId);
		if (CollectionUtils.isEmpty(standardIds)) {
			logger.error("No standards found for the institute");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD, "No standard found for the institute"));
		}

		final List<ExamCreationPayload> examCreationPayloads = createDefaultExamPayloadList(instituteId,
				academicSessionId, standardIds);

		for (final ExamCreationPayload examCreationPayload : examCreationPayloads) {
			createExam(examCreationPayload, instituteId);
		}
		return true;
	}

	private List<ExamCreationPayload> createDefaultExamPayloadList(int instituteId, int academicSessionId,
			List<UUID> standardIds) {
		final List<ExamCreationPayload> examCreationPayloads = new ArrayList<>();
		for (final UUID standardId : standardIds) {
			final ExamCreationPayload examCreationPayload = new ExamCreationPayload();
			examCreationPayload.setAcademicSessionId(academicSessionId);
			examCreationPayload.setStandardId(standardId);
			examCreationPayload.setExamType(ExamType.SYSTEM);
			examCreationPayload.setExamName(EXAM_STRUCTURE);
			examCreationPayloads.add(examCreationPayload);
		}
		return examCreationPayloads;
	}

	private List<Integer> getDimensionIds(int instituteId) {
		final List<ExamDimension> examDimensions = getExamDimensions(instituteId);
		final List<Integer> dimensionIds = new ArrayList<>();
		if (CollectionUtils.isEmpty(examDimensions)) {
			return dimensionIds;
		}
		for (final ExamDimension examDimension : examDimensions) {
			if (examDimension.isTotal()) {
				dimensionIds.add(examDimension.getDimensionId());
			}
		}
		return dimensionIds;
	}

	private List<UUID> getStandardIds(int instituteId, int academicSessionId) {
		final List<Standard> standards = instituteManager.getInstituteStandardList(instituteId, academicSessionId);
		final List<UUID> standardIds = new ArrayList<>();
		if (CollectionUtils.isEmpty(standards)) {
			return standardIds;
		}
		for (final Standard standard : standards) {
			standardIds.add(standard.getStandardId());
		}
		return standardIds;
	}
	private class UpdateDimensionAttributes {
		private final boolean coScholasticGradingEnabled;
		private final boolean scholasticGradingEnabled;
		private final ExamDimension totalNumberExamDimension;
		private final ExamDimension totalGradeExamDimension;
		private final Map<Integer, ExamDimension> examDimensionMap;

		public UpdateDimensionAttributes(boolean coScholasticGradingEnabled, boolean scholasticGradingEnabled,
				ExamDimension totalNumberExamDimension, ExamDimension totalGradeExamDimension,
				Map<Integer, ExamDimension> examDimensionMap) {

			this.coScholasticGradingEnabled = coScholasticGradingEnabled;
			this.scholasticGradingEnabled = scholasticGradingEnabled;
			this.totalNumberExamDimension = totalNumberExamDimension;
			this.totalGradeExamDimension = totalGradeExamDimension;
			this.examDimensionMap = examDimensionMap;
		}

		public boolean isCoScholasticGradingEnabled() {
			return coScholasticGradingEnabled;
		}

		public boolean isScholasticGradingEnabled() {
			return scholasticGradingEnabled;
		}

		public ExamDimension getTotalNumberExamDimension() {
			return totalNumberExamDimension;
		}

		public ExamDimension getTotalGradeExamDimension() {
			return totalGradeExamDimension;
		}

		public Map<Integer, ExamDimension> getExamDimensionMap() {
			return examDimensionMap;
		}

	}

	public Map<Integer, Map<UUID, List<ExamMetaData>>> getSessionStandardExamMetadata(int instituteId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute Id"));
		}
		final Map<Integer, Map<UUID, List<ExamMetaData>>> sessionStandardExamMetadataMap = new HashMap<>();
		final List<ExamMetaData> examMetaDataList = examinationDao.getExamMetadata(instituteId);
		if (CollectionUtils.isEmpty(examMetaDataList)) {
			return sessionStandardExamMetadataMap;
		}

		for (final ExamMetaData examMetaData : examMetaDataList) {
			final int academicSessionId = examMetaData.getAcademicSessionId();
			final UUID standardId = examMetaData.getStandardId();
			if (!sessionStandardExamMetadataMap.containsKey(academicSessionId)) {
				sessionStandardExamMetadataMap.put(academicSessionId, new HashMap<>());
			}
			if (!sessionStandardExamMetadataMap.get(academicSessionId).containsKey(standardId)) {
				sessionStandardExamMetadataMap.get(academicSessionId).put(standardId, new ArrayList<>());
			}
			sessionStandardExamMetadataMap.get(academicSessionId).get(standardId).add(examMetaData);
		}
		return sessionStandardExamMetadataMap;
	}

	public List<ExamMetaData> getExamMetadataByStandard(int instituteId, int academicSessionId, UUID standardId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute Id"));
		}

		if (standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid standard Id"));
		}

		Map<Integer, Map<UUID, List<ExamMetaData>>> examMetaDataMap = getSessionStandardExamMetadata(instituteId);
		if(MapUtils.isEmpty(examMetaDataMap)) {
			return null;
		}
		return ExamMetaData.sortExamByName(examMetaDataMap.get(academicSessionId).get(standardId));
	}

	public boolean addGreenSheetStructure(
			GreenSheetClassStructure<GreenSheetExamDimensionMapData> greenSheetClassStructure) {
		return examinationDao.addGreenSheetStructure(greenSheetClassStructure);
	}

	public GreenSheetClassStructure<GreenSheetExamDimensionMapData> getGreenSheetStructure(int instituteId,
			int academicSessionId, UUID standardId) {
		return examinationDao.getGreenSheetStructure(instituteId, academicSessionId, standardId);
	}

	public Student verifyAndGetStudentForExamResult(UUID instituteUniqueCode, int academicSessionId,
			String admissionNumber, String studentName, String fatherName, String recaptchaAuthenticationKey) {

		if (instituteUniqueCode == null) {
			logger.error("Invalid institute unique code");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute"));
		}

		if (StringUtils.isBlank(admissionNumber)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid admission number"));
		}

		if (StringUtils.isBlank(studentName)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid student name"));
		}

		if (StringUtils.isBlank(fatherName)) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS, "Invalid father name"));
		}

		final boolean verified = googleRecaptchaManager.validateRecaptchaUserToken(recaptchaAuthenticationKey);

		if (!verified) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_CAPTCHA, "Invalid captcha code"));
		}

		final Institute institute = instituteManager.getInstitute(instituteUniqueCode);
		if (institute == null) {
			logger.error("No institute found with unique code {}", instituteUniqueCode);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute"));
		}

		/**
		 * currently restricting result decalation flow for all institutes except 10001
		 */
		if(institute.getInstituteId() != 10001) {
			logger.error("Institute result declaration not setup for institute {}.", institute.getInstituteId());
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
							"Institute result declaration not setup for institute."));
		}

		final Student student = studentManager.getStudentByAcademicSessionAdmissionNumber(institute.getInstituteId(),
				academicSessionId, admissionNumber);

		if (student == null) {
			logger.error("No student found with admission number {}", admissionNumber);
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid admission number"));
		}

		final String storedStudentName = student.getStudentBasicInfo().getName();
		if (!storedStudentName.replaceAll("\\s+", "").equalsIgnoreCase(studentName.replaceAll("\\s+", ""))) {
			logger.error("Student name does not belong to admission number {}", admissionNumber);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
					"Student name does not belong to admission number"));
		}

		final String storedFatherName = student.getStudentFamilyInfo().getFathersName();
		if (StringUtils.isNotBlank(storedFatherName)
				&& !storedFatherName.replaceAll("\\s+", "").equalsIgnoreCase(fatherName.replaceAll("\\s+", ""))) {
			logger.error("Father name does not belong to admission number {}", admissionNumber);
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
					"Father name does not belong to admission number"));
		}

		return student;
	}
	
	public List<StudentExamBasicDetails> getPublishExamDetailsByStudentId(int instituteId, int academicSessionId, UUID studentId) {
		
		if (instituteId <= 0) {
			logger.error("Institue Id cannot be empty");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		
		if (academicSessionId <= 0) {
			logger.error("Academic Session Id cannot be empty");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid Academic Session id"));
		}
		
		if (studentId == null) {
			logger.error("studentId cannot be empty");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid student Id"));
		}
		
		Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId);
		if(student == null) {
			logger.error("Student not present in current session.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student not present in current session."));
		}
		if(student.getStudentStatus() != StudentStatus.ENROLLED) {
			logger.error("Student not Enrolled in the student.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student is not enrolled in the school."
							+ " Please contact school administration."));
		}
		
		StandardMetadata standardMetaData = instituteManager.getStandardMetaData(instituteId,
				academicSessionId, student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId());
		
		final boolean isCoscholasticGradingEnabled = standardMetaData == null ? false
				: standardMetaData.isCoScholasticGradingEnabled();

		final boolean isScholasticGradingEnabled = standardMetaData == null ? false
				: standardMetaData.isScholasticGradingEnabled();
		
		List<ExamCoursesData> examCoursesDatas = getExamCourses(instituteId, academicSessionId,
				student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId());
		
		List<UUID> assignedCourseUUID = getAssignCoursesUUID(instituteId, academicSessionId, studentId);
		
		List<StudentExamBasicDetails> studentExamBasicDetailsList = new ArrayList<StudentExamBasicDetails>();
		if(!CollectionUtils.isEmpty(examCoursesDatas)) {
			for (ExamCoursesData examCoursesData : examCoursesDatas) {
				if (examCoursesData.getExamCoursePublishedStatus() != ExamCoursePublishedStatus.PUBLISHED) {
					continue;
				}
				StudentExamBasicDetails studentExamBasicDetails = new StudentExamBasicDetails(examCoursesData.getExamMetaData().getExamId(),
						examCoursesData.getExamMetaData().getExamName(), getTotalMarksOfExam(examCoursesData, assignedCourseUUID,
						isCoscholasticGradingEnabled, isScholasticGradingEnabled), null,
						isCoscholasticGradingEnabled, isScholasticGradingEnabled,
						examCoursesData.getExamMetaData().getScholasticExamMarksDisplayType(),
						examCoursesData.getExamMetaData().getCoScholasticExamMarksDisplayType());
				studentExamBasicDetailsList.add(studentExamBasicDetails);
			}
		}
		return studentExamBasicDetailsList;
	}
	
	private List<UUID> getAssignCoursesUUID(int instituteId, int academicSessionId, UUID studentId) {
		List<Course> courseList = courseManager.getStudentAssignedCourses(instituteId, studentId, academicSessionId);
		
		List<UUID> assignedCourseUUID = new ArrayList<UUID>();
		for(Course course : courseList) {
			assignedCourseUUID.add(course.getCourseId());
		}
		
		return assignedCourseUUID;		
	}

	private double getTotalMarksOfExam(ExamCoursesData examCoursesData, List<UUID> assignedCourseUUID, 
			boolean isCoscholasticGradingEnabled, boolean isScholasticGradingEnabled) {
		double totalMarks = 0d;  
		for(ExamCourse examCourse : examCoursesData.getExamCourses()) {
			if(!assignedCourseUUID.contains(examCourse.getCourse().getCourseId())) {
				continue;
			}
			
			/**
			 * checking if course is coscholastic, if so continue as we don't want to add total marks for coscholastic subjects
			 */
			if(examCourse.getCourse().getCourseType() == CourseType.COSCHOLASTIC && isCoscholasticGradingEnabled) {
				continue;
			}

			/**
			 * checking if course is scholastic and it is grading, if so continue
			 */
			if(examCourse.getCourse().getCourseType() == CourseType.SCHOLASTIC && isScholasticGradingEnabled) {
				continue;
			}
			
			for(ExamDimensionValues examDimensionValues : examCourse.getExamDimensionValues()) {
				if(examDimensionValues.getExamCoursePublishedStatus() != ExamCoursePublishedStatus.PUBLISHED) {
					continue;
				}
				totalMarks += examDimensionValues.getMaxMarks() == null ? 0d : examDimensionValues.getMaxMarks();;
			}
		}
		return totalMarks;
	}

	public StudentViewExamMarksDetails getSingleStudentExamMarksDetails(int instituteId, int academicSessionId,
			UUID examId, UUID studentId) {
		Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId, studentId);
		if(student == null) {
			logger.error("Student not present in current session.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student not present in current session."));
		}
		if(student.getStudentStatus() != StudentStatus.ENROLLED) {
			logger.error("Student not Enrolled in the student.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student is not enrolled in the school."
							+ " Please contact school administration."));
		}
		
		Standard standard = student.getStudentAcademicSessionInfoResponse().getStandard();
		StudentExamMarksDetails studentExamMarksDetails = getStudentExamMarksDetail(instituteId, examId, 
				CollectionUtils.isEmpty(standard.getStandardSectionList()) 
				? null : standard.getStandardSectionList().get(0).getSectionId(), studentId);
		
		return getStudentViewExamMarksDetails(instituteId, academicSessionId, standard.getStandardId(), studentExamMarksDetails.getExamMetaData(),
				getStudentAssignedExamCourseMarks(instituteId, studentId, academicSessionId, studentExamMarksDetails));
	}
	
	private List<ExamCourseMarks> getStudentAssignedExamCourseMarks(int instituteId, UUID studentId, int academicSessionId,
			StudentExamMarksDetails studentExamMarksDetails) {
		if(studentExamMarksDetails == null || CollectionUtils.isEmpty(studentExamMarksDetails.getExamCoursesAllDimensionsMarks())) {
			return null;
		}
		
		List<UUID> assignedCourseUUID = getAssignCoursesUUID(instituteId, academicSessionId, studentId);
		
		List<ExamCourseMarks> finalExamCourseMarksList =  new ArrayList<ExamCourseMarks>();
		for(ExamCourseMarks examCourseMarks : studentExamMarksDetails.getExamCoursesAllDimensionsMarks()) {
			if(assignedCourseUUID.contains(examCourseMarks.getCourse().getCourseId())) {
				finalExamCourseMarksList.add(examCourseMarks);
			}
		}		
		
		return finalExamCourseMarksList;
	}

	private StudentViewExamMarksDetails getStudentViewExamMarksDetails(int instituteId, int academicSessionId, 
			UUID standardId, ExamMetaData examMetaData, List<ExamCourseMarks> finalExamCourseMarksList) {
		
		final Map<CourseType, List<ExamGrade>> courseTypeExamGrades = getExamGrades(instituteId,
				academicSessionId, standardId);
		Map<CourseType, List<ExamMarksDetails>> courseTypeExamMarksDetailsMap = new HashMap<CourseType, List<ExamMarksDetails>>();
		for(ExamCourseMarks examCourseMarks : finalExamCourseMarksList) {
			CourseType courseType = examCourseMarks.getCourse().getCourseType();
			List<ExamDimensionMarksDetails> examDimensionMarksDetailsList = new ArrayList<ExamDimensionMarksDetails>(); 
			for(ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()) {
				
				/**
				 * checking if exam is published or not
				 */
				if(examDimensionObtainedValues.getExamCoursePublishedStatus() != ExamCoursePublishedStatus.PUBLISHED) {
					continue;
				}
				
				/**
				 * checking if exam contain only total dimension, then let it go ow continue 
				 */
				if(examCourseMarks.getExamDimensionObtainedValues().size() > 1 && 
						examDimensionObtainedValues.getExamDimension().isTotal()) {
					continue;
				}
				
				/**
				 * checking if marks are saved or not
				 */
				if(examDimensionObtainedValues.getMarksFeedStatus() == MarksFeedStatus.SAVED) {
					continue;
				}

				ExamDimensionMarksDetails examDimensionMarksDetails = new ExamDimensionMarksDetails(
						examDimensionObtainedValues.getExamDimension().getDimensionId(),
						examDimensionObtainedValues.getExamDimension().getDimensionName(),
						examDimensionObtainedValues.getObtainedGrade(), examDimensionObtainedValues.getMaxGrade(), 
						examDimensionObtainedValues.getObtainedMarks(),
						examDimensionObtainedValues.getGraceMarks(), examDimensionObtainedValues.getMaxMarks(),
						examDimensionObtainedValues.getExamDimension().isTotal(),
						examDimensionObtainedValues.getExamDimension().getExamEvaluationType(),
						examDimensionObtainedValues.getAttendanceStatus(), examDimensionObtainedValues.getMaxStar(),
						examDimensionObtainedValues.getObtainedStar());
				examDimensionMarksDetailsList.add(examDimensionMarksDetails);
			}
			
			TotalMarksDetails totalMarksDetails = ExamMarksUtils.getTotalMarksDetails(
					examDimensionMarksDetailsList, courseTypeExamGrades.get(courseType));

			ExamMarksDetails examMarksDetails = new ExamMarksDetails(examCourseMarks.getCourse().getCourseId(),
					examCourseMarks.getCourse().getCourseName(), totalMarksDetails.getObtainedGrade(), 
					totalMarksDetails.getMaxGrade(), totalMarksDetails.getObtainedMarks(), totalMarksDetails.getGraceMarks(), 
					totalMarksDetails.getTotalMarks(), totalMarksDetails.getExamAttendanceStatus(), totalMarksDetails.getMaxStars(), totalMarksDetails.getObtainedStars(),  examDimensionMarksDetailsList);
			
			if(courseTypeExamMarksDetailsMap.containsKey(courseType)) {
				courseTypeExamMarksDetailsMap.get(courseType).add(examMarksDetails);
			} else {
				List<ExamMarksDetails> examMarksDetailsList = new ArrayList<ExamMarksDetails>();
				examMarksDetailsList.add(examMarksDetails);
				courseTypeExamMarksDetailsMap.put(courseType, examMarksDetailsList);
			}
		}
		
		Map<CourseType, TotalMarksDetails> courseTypeExamDimensionMarksDetailsMap = new HashMap<CourseType, TotalMarksDetails>();
		for(Entry<CourseType, List<ExamMarksDetails>> entry : courseTypeExamMarksDetailsMap.entrySet()) {
			if(!courseTypeExamDimensionMarksDetailsMap.containsKey(entry.getKey())) {
				TotalMarksDetails totalMarksDetails = ExamMarksUtils.getTotalMarksDetail(
						entry.getValue(), courseTypeExamGrades.get(entry.getKey()));
				courseTypeExamDimensionMarksDetailsMap.put(entry.getKey(), totalMarksDetails);
			}
		}
		return new StudentViewExamMarksDetails(examMetaData.getScholasticExamMarksDisplayType(),  examMetaData.getCoScholasticExamMarksDisplayType(), courseTypeExamMarksDetailsMap, courseTypeExamDimensionMarksDetailsMap);
	}

	public StudentExamMarksDetails getStudentExamMarksDetail(int instituteId, UUID examId, Integer sectionId, UUID studentId) { 
		if (instituteId <= 0) {
			logger.error("Institue Id cannot be empty");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id"));
		}
		
		if (examId == null) {
			logger.error("examId cannot be empty");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid Exam Id"));
		}
		
		return getStudentExamMarksDetails(getResolvedClassMarks(instituteId, examId, sectionId, false), studentId);
	}
	
	public StudentExamMarksDetails getStudentExamMarksDetails(List<StudentExamMarksDetails> studentExamMarksDetailsList,
			UUID studentId) {
		if(CollectionUtils.isEmpty(studentExamMarksDetailsList)) {
			return null;
		}
		for(StudentExamMarksDetails studentExamMarksDetailsRow : studentExamMarksDetailsList) {
			UUID currentStudentId = studentExamMarksDetailsRow.getStudent().getStudentId();
			if(studentId.equals(currentStudentId)) {
				return studentExamMarksDetailsRow;
			}
		}
		return null;
	}

	public boolean addDatesheet(UUID userId, int instituteId, DatesheetAndSyllabusPayload datesheetAndSyllabusPayload) {
		if(instituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid institute Id"));
		}
		if(userId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid User Id"));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId,
				AuthorisationRequiredAction.ADD_DATESHEET);
		validateDatesheet(datesheetAndSyllabusPayload, false);
		if(examinationDao.getDatesheetDetailsByExamId(instituteId, datesheetAndSyllabusPayload.getAcademicSessionId(),
				datesheetAndSyllabusPayload.getStandardId(), datesheetAndSyllabusPayload.getExamId()) != null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Datesheet already created for this exam. Please update that."));
		}
		return examinationDao.addDatesheet(datesheetAndSyllabusPayload, userId);
	}

	public void validateDatesheet(DatesheetAndSyllabusPayload datesheetAndSyllabusPayload, boolean update){
		if(datesheetAndSyllabusPayload == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Datesheet can not be null."));
		}
		if(datesheetAndSyllabusPayload.getInstituteId() <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Institute Id."));
		}
		if(datesheetAndSyllabusPayload.getAcademicSessionId() <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Session Id."));
		}
        if(update){
            if(datesheetAndSyllabusPayload.getDatesheetId() == null){
                throw new ApplicationException(
                        new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Datesheet Id."));
            }
        }
		if(datesheetAndSyllabusPayload.getStandardId() == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Standard Id."));
		}
		if(datesheetAndSyllabusPayload.getExamId() == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Exam Id."));
		}
		if(datesheetAndSyllabusPayload.getExamStartDate() <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Start Date."));
		}
		if(datesheetAndSyllabusPayload.getExamEndDate() <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid End Date."));
		}
		if(datesheetAndSyllabusPayload.getExamStartDate() > datesheetAndSyllabusPayload.getExamEndDate()) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Exam end date should be greater than exam end date."));
		}
		if(datesheetAndSyllabusPayload.getDatesheetStatus() == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Status."));
		}
		if(datesheetAndSyllabusPayload.getCourseWiseDatesheetList().isEmpty()){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Course cannot be null."));
		}
		validateCourseWiseDatesheet(datesheetAndSyllabusPayload.getCourseWiseDatesheetList(),
				datesheetAndSyllabusPayload.getExamStartDate(), datesheetAndSyllabusPayload.getExamEndDate());
	}

	private void validateCourseWiseDatesheet(List<CourseWiseDatesheetPayload> courseWiseDatesheetList,
											 Integer examStartDate, Integer examEndDate) {
		for(CourseWiseDatesheetPayload courseWiseDatesheetPayload : courseWiseDatesheetList) {
			if(courseWiseDatesheetPayload.getCourseId() == null){
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Course."));
			}
			if(courseWiseDatesheetPayload.getDimensionWiseDatesheetList().isEmpty()){
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Dimension cannot be null."));
			}
			validateDimensionWiseDatesheet(courseWiseDatesheetPayload.getDimensionWiseDatesheetList(), examStartDate, examEndDate);
		}
	}

	private void validateDimensionWiseDatesheet(List<DimensionWiseDatesheetPayload> dimensionWiseDatesheetList,
												Integer examStartDate, Integer examEndDate) {
		for(DimensionWiseDatesheetPayload dimensionWiseDatesheetPayload : dimensionWiseDatesheetList){
			if(dimensionWiseDatesheetPayload.getDimensionId() <= 0){
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Dimension Id."));
			}
			if(dimensionWiseDatesheetPayload.getCourseExamDate() <= 0){
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Exam Date."));
			}

			if(dimensionWiseDatesheetPayload.getCourseExamDate() < examStartDate) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Course Exam Date cannot be before exam start date."));
			}
			if(dimensionWiseDatesheetPayload.getCourseExamDate() > examEndDate) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Course Exam Date cannot be after exam end date."));
			}
			//TODO: check exam end time should be greater than exam end time
			if(dimensionWiseDatesheetPayload.getStartTime().compareTo(dimensionWiseDatesheetPayload.getEndTime()) >= 0) {
				throw new ApplicationException(
						new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Course Exam end time should be greater then start time."));
			}
		}
	}


	public boolean updateDatesheet(UUID userId, int instituteId, DatesheetAndSyllabusPayload datesheetAndSyllabusPayload) {
		if(instituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid institute Id"));
		}
		if(userId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid User Id"));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId,
				AuthorisationRequiredAction.UPDATE_DATESHEET);
		validateDatesheet(datesheetAndSyllabusPayload, true);
		return examinationDao.updateDatesheet(datesheetAndSyllabusPayload, userId);
	}


	public boolean deleteDatesheet(UUID userId, int instituteId, UUID datesheetId) {
		if(instituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid institute Id"));
		}
		if(userId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid User Id"));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId,
				AuthorisationRequiredAction.DELETE_DATESHEET);
		return examinationDao.deleteDatesheet(datesheetId);
	}

	public List<DatesheetMetadata> getDatesheet(int instituteId, int academic_session_id, UUID standardId) {
		if(academic_session_id <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Academic Session Id"));
		}
		if(instituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Institute Id"));
		}
		if(standardId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Standard Id"));
		}
		return DatesheetMetadata.sortDatesheetByExamName(
				examinationDao.getDatesheet(instituteId, academic_session_id, standardId));
	}

	public List<DatesheetMetadata> getStudentDatesheet(int instituteId, int academic_session_id, UUID studentId) {
		if(academic_session_id <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Academic Session Id"));
		}
		if(instituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Institute Id"));
		}
		if(studentId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Standard Id"));
		}
		Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academic_session_id, studentId);
		if(student == null) {
			logger.error("Student not present in current session.");
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_STUDENT, "Student not present in current session."));
		}
		UUID standardId = student.getStudentAcademicSessionInfoResponse().getStandard().getStandardId();
		if(standardId == null) {
			return null;
		}
		return getDatesheet(instituteId, academic_session_id, standardId);
	}

	public DatesheetDetails getDatesheetDetailsByDatesheetId(int instituteId, int academicSessionId, UUID standardId, UUID datesheetId) {
		if (academicSessionId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Academic Session Id"));
		}
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Institute Id"));
		}
		if (standardId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Standard Id"));
		}
		if (datesheetId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Datesheet Id"));
		}
		return examinationDao.getDatesheetDetailsByDatesheetId(instituteId,
				academicSessionId, standardId, datesheetId);
	}

	public DatesheetDetails getDatesheetDetailsByExamId(int instituteId, int academicSessionId, UUID standardId, UUID examId) {
		if(academicSessionId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Academic Session Id"));
		}
		if(instituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Institute Id"));
		}
		if(standardId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Standard Id"));
		}
		if(examId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Datesheet Id"));
		}
		return examinationDao.getDatesheetDetailsByExamId(instituteId, academicSessionId, standardId, examId);
	}

	public Map<Integer, List<DatesheetDetailRow>> getSortedDatesheetDetailRow(int instituteId, int academicSessionId, UUID standardId, UUID examId,
																			  UUID datesheetId) {
		if(academicSessionId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Academic Session Id"));
		}
		if(instituteId <= 0){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Institute Id"));
		}
		if(standardId == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_DATESHEET, "Invalid Standard Id"));
		}
		return sortDatesheetByStartTime(examinationDao.getSortedDatesheetDetailRow(instituteId, academicSessionId, standardId, examId, datesheetId));
	}

	private Map<Integer, List<DatesheetDetailRow>> sortDatesheetByStartTime(Map<Integer, List<DatesheetDetailRow>> sortedDatesheetDetailRow) {

		if(sortedDatesheetDetailRow == null || CollectionUtils.isEmpty(sortedDatesheetDetailRow.entrySet())) {
			return null;
		}

		for(Entry<Integer, List<DatesheetDetailRow>> entry : sortedDatesheetDetailRow.entrySet()) {
			Collections.sort(entry.getValue(), new Comparator<DatesheetDetailRow>() {
				@Override
				public int compare(DatesheetDetailRow s1, DatesheetDetailRow s2) {
					return s1.getStartTime().compareTo(s2.getStartTime());
				}
			});
		}

		return sortedDatesheetDetailRow;
	}

	public boolean updateExamCoursesStatus(int instituteId, UUID userId, UUID examId,
									  ExamCoursePublishedStatus examCoursePublishedStatus) {
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.PUBLISH_EXAM_MARKS);
		if(examCoursePublishedStatus == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid examCoursePublishedStatus Id"));
		}
		if(examId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Exam Id cannot be null"));
		}
		return examinationDao.updateExamCoursesStatus(examId, examCoursePublishedStatus);
	}

	public List<CourseDimensionSectionMarksStatusDetails> getCourseDimensionSectionMarksStatusDetails(int instituteId, int academicSessionId, UUID examId, UUID userId) {

		ExamDetails examDetails = getExamDetails(examId, instituteId);
		UUID standardId = examDetails.getStandard().getStandardId();
		List<ExamCoursesData> examCoursesDataList = getExamCourses(instituteId, academicSessionId, standardId, false, true);
		ExamCoursesData examCoursesData = null;
		for(ExamCoursesData examCoursesData1 : examCoursesDataList) {
			if(examCoursesData1.getExamMetaData().getExamId().equals(examId)) {
				examCoursesData = examCoursesData1;
				break;
			}
		}

		if(CollectionUtils.isEmpty(examCoursesDataList)) {
			return null;
		}

		List<Standard> standardList = instituteManager.getInstituteStandardList(instituteId, academicSessionId);
		Standard standard = null;
		for(Standard standard1 : standardList) {
			if(standard1.getStandardId().equals(standardId)) {
				standard = standard1;
				break;
			}
		}

		//CourseId, DimensionId:SectionId, Status
		Map<UUID, Map<String, MarksFeedStatus>> courseDimensionSectionStatusMap = new HashMap<>();
		Map<UUID, Course> courseMap = new HashMap<>();
		Map<UUID, Map<Integer, ExamDimensionValues>> courseDimensionMap = new HashMap<>();
		Map<Integer, StandardSections> sectionMap = new HashMap<>();
		for(ExamCourse examCourse : examCoursesData.getExamCourses()) {
			Course course = examCourse.getCourse();
			UUID courseId = course.getCourseId();
			if(!courseDimensionSectionStatusMap.containsKey(courseId)) {
				courseDimensionSectionStatusMap.put(courseId, new HashMap<>());
				courseMap.put(courseId, course);
			}
			if(!courseDimensionMap.containsKey(courseId)) {
				courseDimensionMap.put(courseId, new HashMap<>());
			}
			Map<Integer, ExamDimensionValues> dimensionMap = courseDimensionMap.get(courseId);
			for (ExamDimensionValues examDimensionValues : examCourse.getExamDimensionValues()) {
				MarksFeedStatus marksFeedStatus = null;
				ExamDimension examDimension = examDimensionValues.getExamDimension();
				int dimensionId = examDimension.getDimensionId();
				if(standard != null && !CollectionUtils.isEmpty(standard.getStandardSectionList())) {
					for (StandardSections standardSections : standard.getStandardSectionList()) {
						int sectionId = standardSections.getSectionId();
						if(!sectionMap.containsKey(sectionId)) {
							sectionMap.put(sectionId, standardSections);
						}
						String dimensionSectionId = dimensionId + (":" + sectionId);
						if (!courseDimensionSectionStatusMap.get(courseId).containsKey(dimensionSectionId)) {
							courseDimensionSectionStatusMap.get(courseId).put(dimensionSectionId, marksFeedStatus);
							dimensionMap.put(dimensionId, examDimensionValues);
						}
					}
				} else {
					String dimensionSectionId = String.valueOf(dimensionId);
					if (!courseDimensionSectionStatusMap.get(courseId).containsKey(dimensionSectionId)) {
						courseDimensionSectionStatusMap.get(courseId).put(dimensionSectionId, marksFeedStatus);
						dimensionMap.put(dimensionId, examDimensionValues);
					}
				}
			}
		}

		final List<StudentExamMarksDetails> studentExamMarksDetailsListt = getClassMarksByCourseList(
				instituteId, academicSessionId, examId, null, null, null, false, userId);

		Map<String, List<StudentExamMarksDetails>> studentExamMarksDetailsMap = new HashMap<>();
		List<StudentExamMarksDetails> studentExamMarksDetailsList = new ArrayList<>();
		List<Integer> sectionIdSet = new ArrayList<>();
		for(StudentExamMarksDetails studentExamMarksDetail : studentExamMarksDetailsListt) {
			String studentStandardId = studentExamMarksDetail.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardId().toString();
			List<StandardSections> standardSectionsList = studentExamMarksDetail.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
			if(CollectionUtils.isEmpty(standardSectionsList)) {
				studentExamMarksDetailsList.add(studentExamMarksDetail);
				studentExamMarksDetailsMap.put(studentStandardId, studentExamMarksDetailsListt);
				break;
			}
			int sectionId = studentExamMarksDetail.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList().get(0).getSectionId();
			String standardSectionString = studentStandardId + ":" + sectionId;
			if(!sectionIdSet.contains(sectionId)) {
				studentExamMarksDetailsList.add(studentExamMarksDetail);
				sectionIdSet.add(sectionId);
				studentExamMarksDetailsMap.put(standardSectionString, new ArrayList<>());
			}
			studentExamMarksDetailsMap.get(standardSectionString).add(studentExamMarksDetail);
		}

		for(StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			String studentStandardId = studentExamMarksDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardId().toString();
			List<StandardSections> standardSectionsList = studentExamMarksDetails.getStudent().getStudentAcademicSessionInfoResponse().getStandard().getStandardSectionList();
			StandardSections standardSections = CollectionUtils.isEmpty(standardSectionsList) ? null : standardSectionsList.get(0);
			Integer sectionId = standardSections == null ? null : standardSections.getSectionId();
			String standardSectionString = studentStandardId + (sectionId == null ? "" : ":" + sectionId);
			List<StudentExamMarksDetails> list = studentExamMarksDetailsMap.get(standardSectionString);
			List<ExamCourseMarks> examCourseMarksList = studentExamMarksDetails.getExamCoursesAllDimensionsMarks();
			for (ExamCourseMarks examCourseMarks : examCourseMarksList) {
				Course course = examCourseMarks.getCourse();
				UUID courseId = course.getCourseId();
				for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()) {
					MarksFeedStatus marksFeedStatus = computeSectionStandardStudentsMarksStatus(list, courseId, examDimensionObtainedValues.getExamDimension().getDimensionId());
					ExamDimension examDimension = examDimensionObtainedValues.getExamDimension();
					int dimensionId = examDimension.getDimensionId();
					String dimensionSectionId = dimensionId + (sectionId == null ? "" : (":" + sectionId));
					courseDimensionSectionStatusMap.get(courseId).put(dimensionSectionId, marksFeedStatus);
				}
			}
		}

		Map<UUID, CourseDimensionSectionMarksStatusDetails> courseDimensionSectionMarksStatusDetailsMap = new HashMap<>();
		for(Entry<UUID, Map<String, MarksFeedStatus>> mapEntry : courseDimensionSectionStatusMap.entrySet()) {
			UUID courseId = mapEntry.getKey();
			Course course = courseMap.get(courseId);
			Map<Integer, ExamDimensionValues> dimensionMap = courseDimensionMap.get(courseId);
			Map<Integer, DimensionSectionMarksStatusDetails> dimensionSectionMarksStatusDetailsMap = new HashMap<>();
			Map<Integer, List<Integer>> dimensionSectionIdMap = new HashMap<>();
			for(Entry<String, MarksFeedStatus> mapEntry2 : mapEntry.getValue().entrySet()) {
				String dimensionSectionId = mapEntry2.getKey();
				String [] token = dimensionSectionId.split(":");
				int dimensionId = Integer.parseInt(token[0]);
				Integer sectionId = null;
				if(token.length > 1) {
					sectionId = Integer.parseInt(token[1]);
				}
				ExamDimensionValues examDimensionValues = dimensionMap.get(dimensionId);

				//if evaluation type is number and then if its total dimension , skip it and if max marks is not filled, skip
				if (examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER &&
						(examDimensionValues.getExamDimension().isTotal() || examDimensionValues.getOriginalMaxMarks() == null)) {
					continue;
				}

//				if(examDimensionValues.getExamDimension().isTotal()) {
//					continue;
//				}
//
//				if(examDimensionValues.getExamDimension().getExamEvaluationType() == ExamEvaluationType.NUMBER &&
//						examDimensionValues.getOriginalMaxMarks() == null) {
//					continue;
//				}

				if(!dimensionSectionIdMap.containsKey(dimensionId)) {
					dimensionSectionIdMap.put(dimensionId, new ArrayList<>());
				}
				StandardSections standardSections = sectionId == null ? null : sectionMap.get(sectionId);
				MarksFeedStatus marksFeedStatus = mapEntry2.getValue();
				if(dimensionSectionMarksStatusDetailsMap.containsKey(dimensionId)) {
					List<SectionMarksStatusDetails> sectionMarksStatusDetailsList = new ArrayList<>();
					if(sectionId != null && !dimensionSectionIdMap.get(dimensionId).contains(sectionId)) {
						sectionMarksStatusDetailsList.add(
								new SectionMarksStatusDetails(standardSections, marksFeedStatus));
						dimensionSectionIdMap.get(dimensionId).add(sectionId);
					}
					dimensionSectionMarksStatusDetailsMap.get(dimensionId).getSectionMarksStatusDetailsList().addAll(sectionMarksStatusDetailsList);
					dimensionSectionMarksStatusDetailsMap.get(dimensionId).setSectionCountManual();
				} else {
					List<SectionMarksStatusDetails> sectionMarksStatusDetailsList = new ArrayList<>();
					sectionMarksStatusDetailsList.add(new SectionMarksStatusDetails(standardSections, marksFeedStatus));
					if(sectionId != null) {
						dimensionSectionIdMap.get(dimensionId).add(sectionId);
					}
					DimensionSectionMarksStatusDetails dimensionSectionMarksStatusDetails = new DimensionSectionMarksStatusDetails(examDimensionValues,
							sectionMarksStatusDetailsList);
					dimensionSectionMarksStatusDetailsMap.put(dimensionId, dimensionSectionMarksStatusDetails);
				}
			}
			if(!courseDimensionSectionMarksStatusDetailsMap.containsKey(courseId)) {
				CourseDimensionSectionMarksStatusDetails courseDimensionSectionMarksStatusDetails = new CourseDimensionSectionMarksStatusDetails(course,
						new ArrayList<>(dimensionSectionMarksStatusDetailsMap.values()));
				courseDimensionSectionMarksStatusDetailsMap.put(courseId, courseDimensionSectionMarksStatusDetails);
			}
		}
		return CourseDimensionSectionMarksStatusDetails.sort(new ArrayList<>(courseDimensionSectionMarksStatusDetailsMap.values()));
	}

	private MarksFeedStatus computeSectionStandardStudentsMarksStatus(List<StudentExamMarksDetails> studentExamMarksDetailsList, UUID courseId , int dimensionId) {
		if(CollectionUtils.isEmpty(studentExamMarksDetailsList)) {
			return null;
		}
		for(StudentExamMarksDetails studentExamMarksDetails : studentExamMarksDetailsList) {
			List<ExamCourseMarks> examCourseMarksList = studentExamMarksDetails.getExamCoursesAllDimensionsMarks();
			for (ExamCourseMarks examCourseMarks : examCourseMarksList) {
				if(!examCourseMarks.getCourse().getCourseId().equals(courseId)){
					continue;
				}
				for (ExamDimensionObtainedValues examDimensionObtainedValues : examCourseMarks.getExamDimensionObtainedValues()) {
					if(examDimensionObtainedValues.getExamDimension().getDimensionId() != dimensionId){
						continue;
					}
					MarksFeedStatus marksFeedStatus = examDimensionObtainedValues.getMarksFeedStatus();
					if(marksFeedStatus == null) {
						continue;
					}
					switch (marksFeedStatus) {
						case SUBMITTED:
							return MarksFeedStatus.SUBMITTED;
						case SAVED:
							return MarksFeedStatus.SAVED;
					}
				}
			}
		}
		return null;
	}

	public boolean updateExamMarksFeedStatus(int instituteId, int academicSessionId, UUID userId,
											 MarksFeedStatus marksFeedStatus, UUID examId, UUID courseId, int dimensionId, UUID standardId, Integer sectionId) {
		if (instituteId <= 0) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid institute Id"));
		}

		if (userId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user Id"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.BULK_UPDATE_MARKS_STATUS);

		verifyExaminationDataAccessibility(instituteId, academicSessionId, standardId, sectionId, courseId, userId, true);

		if (marksFeedStatus == null) {
			logger.error("Invalid marks feed status");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Invalid Marks feed status"));
		}

		if (examId == null) {
			logger.error("Invalid exam details");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Invalid exam details"));
		}

		if (courseId == null) {
			logger.error("Invalid course details");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Invalid course details"));
		}

		if (dimensionId <= 0) {
			logger.error("Invalid dimension details");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_EXAM_CONFIGURATION,
					"Invalid dimension details"));
		}

		return examinationDao.updateExamMarksFeedStatus(academicSessionId, marksFeedStatus, examId, courseId, dimensionId, sectionId);
	}

	public boolean addPersonalityTraitsDetails(int instituteId, int academicSessionId, UUID userId, List<PersonalityTraitsDetails> personalityTraitsDetailList) {

		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}

		if(userId == null){
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId,userId, AuthorisationRequiredAction.EDIT_PERSONALITY_TRAITS);
		validatePersonalityTraitsDetails(instituteId, academicSessionId, personalityTraitsDetailList, false);

		return examinationDao.addPersonalityTraitsDetails(instituteId, personalityTraitsDetailList);
	}

	public boolean updatePersonalityTraitsDetails(int instituteId, int academicSessionId, UUID userId, List<PersonalityTraitsDetails> personalityTraitsDetailList){

		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}

		if(userId == null){
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_PERSONALITY_TRAITS);
		validatePersonalityTraitsDetails(instituteId, academicSessionId, personalityTraitsDetailList, true);

		return examinationDao.updatePersonalityTraitsDetails(personalityTraitsDetailList);
	}

	public void validatePersonalityTraitsDetails(int instituteId, int academicSessionId, List<PersonalityTraitsDetails> personalityTraitsDetailList, boolean update){
		if(CollectionUtils.isEmpty(personalityTraitsDetailList)) {
			throw new ApplicationException(new ErrorResponse(
					ApplicationErrorCode.INVALID_INSTITUTE, "Invalid personality trait payload."));
		}

		// Map to track personality trait names by standard ID within the payload
		Map<UUID, Set<String>> standardTraitNamesMap = new HashMap<>();
		// Set to collect unique standard IDs for batch database query
		Set<UUID> uniqueStandardIds = new HashSet<>();

		// First pass: Basic validation and collect data for batch operations
		for(PersonalityTraitsDetails personalityTraitsDetails : personalityTraitsDetailList) {

			if (personalityTraitsDetails.getAcademicSessionId() <= 0) {
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
			}

			if (personalityTraitsDetails.getStandardId() == null) {
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_REQUEST, "Invalid Standard id."));
			}

			if (StringUtils.isBlank(personalityTraitsDetails.getPersonalityTraitName())) {
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_REQUEST, "Invalid personality trait name."));
			}

			if(update) {
				if (personalityTraitsDetails.getPersonalityTraitId() == null) {
					throw new ApplicationException(new ErrorResponse(
							ApplicationErrorCode.INVALID_REQUEST, "Invalid personality trait id."));
				}
			}

			// Check for duplicates within the payload for the same standard
			UUID standardId = personalityTraitsDetails.getStandardId();
			String traitName = personalityTraitsDetails.getPersonalityTraitName().trim();

			if (!standardTraitNamesMap.containsKey(standardId)) {
				standardTraitNamesMap.put(standardId, new HashSet<String>());
			}

			if (!standardTraitNamesMap.get(standardId).add(traitName.toLowerCase())) {
				throw new ApplicationException(new ErrorResponse(
						ApplicationErrorCode.INVALID_REQUEST, "Duplicate personality trait name '" + traitName + "' found for the same standard in the request."));
			}

			// Collect unique standard IDs for batch database query
			uniqueStandardIds.add(standardId);
		}

		// Batch database call: Get all existing personality traits for all standards in one query
		Map<UUID, List<PersonalityTraitsDetails>> existingTraitsByStandard = new HashMap<>();
		for (UUID standardId : uniqueStandardIds) {
			List<PersonalityTraitsDetails> existingTraits = examinationDao.getPersonalityTraits(instituteId, academicSessionId, standardId);
			if (!CollectionUtils.isEmpty(existingTraits)) {
				existingTraitsByStandard.put(standardId, existingTraits);
			}
		}

		// Second pass: Check for duplicates against existing database records
		for(PersonalityTraitsDetails personalityTraitsDetails : personalityTraitsDetailList) {
			UUID standardId = personalityTraitsDetails.getStandardId();
			String traitName = personalityTraitsDetails.getPersonalityTraitName().trim();

			List<PersonalityTraitsDetails> existingTraits = existingTraitsByStandard.get(standardId);
			if (!CollectionUtils.isEmpty(existingTraits)) {
				for (PersonalityTraitsDetails existingTrait : existingTraits) {
					if (existingTrait.getPersonalityTraitName().trim().equalsIgnoreCase(traitName)) {
						if (update) {
							// For update, allow if it's the same trait being renamed (same ID)
							if (!existingTrait.getPersonalityTraitId().equals(personalityTraitsDetails.getPersonalityTraitId())) {
								throw new ApplicationException(new ErrorResponse(
										ApplicationErrorCode.INVALID_REQUEST, "Personality trait name '" + traitName + "' already exists for this standard."));
							}
						} else {
							// For add, don't allow any duplicates
							throw new ApplicationException(new ErrorResponse(
									ApplicationErrorCode.INVALID_REQUEST, "Personality trait name '" + traitName + "' already exists for this standard."));
						}
						break; // Found a match, no need to continue checking this trait
					}
				}
			}
		}
	}

	public boolean deletePersonalityTraitsDetails(int instituteId, int academicSessionId, UUID userId, UUID standardId,
												  List<UUID> personalityTraitIdList) {

		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}

		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid user id."));
		}

		if (org.springframework.util.CollectionUtils.isEmpty(personalityTraitIdList)) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Personality trait List cannot be empty."));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.EDIT_PERSONALITY_TRAITS);

		//TODO:fetch student personality trait details - if there is throw error.
		return examinationDao.deletePersonalityTraitsDetails(instituteId, academicSessionId, personalityTraitIdList, standardId);

	}


	public List<StandardPersonalityTraitsDetails> getInstitutePersonalityTraits(int instituteId, int academicSessionId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}

		/**
		 * fetching standard without session as we don't need sections in this flow
		 * and session will be handled by personality traits
		 * standard here is just for name display
		 */
		List<Standard> standardList = instituteManager.getInstituteStandardList(instituteId);
		List<PersonalityTraitsDetails> personalityTraitsDetailsList = examinationDao.getPersonalityTraits(instituteId, academicSessionId, null);
		Map<UUID, List<PersonalityTraitsDetails>> standardPersonalityTraitMap = new HashMap<>();
		if(!CollectionUtils.isEmpty(personalityTraitsDetailsList)) {
			for (PersonalityTraitsDetails personalityTraitsDetails : personalityTraitsDetailsList) {
				if (!standardPersonalityTraitMap.containsKey(personalityTraitsDetails.getStandardId())) {
					standardPersonalityTraitMap.put(personalityTraitsDetails.getStandardId(), new ArrayList<>());
				}
				standardPersonalityTraitMap.get(personalityTraitsDetails.getStandardId()).add(personalityTraitsDetails);
			}
		}

		List<StandardPersonalityTraitsDetails> standardPersonalityTraitsDetailsList = new ArrayList<>();
		for(Standard standard : standardList) {
			standardPersonalityTraitsDetailsList.add(new StandardPersonalityTraitsDetails(standard,
					standardPersonalityTraitMap.get(standard.getStandardId())));
		}
		return standardPersonalityTraitsDetailsList;
	}

	public List<PersonalityTraitsDetails> getStandardPersonalityTraits(int instituteId, int academicSessionId, UUID standardId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}

		return PersonalityTraitsDetails.sortPersonalityTraitsDetailsBySequence(examinationDao.getPersonalityTraits(instituteId, academicSessionId, standardId));
	}


//	-----Student Report Card Status APIs-----
	public List<StudentReportCardStatusDetails> getStudentReportCardStatusDetails(int instituteId, int academicSessionId,
																				  UUID standardId, Set<Integer> sectionIdSet, UUID reportCardId, UUID studentId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE, "Invalid institute id."));
		}

		if (academicSessionId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid academic session id."));
		}
		return examinationDao.getStudentReportCardStatusDetails(instituteId, academicSessionId, standardId, sectionIdSet, reportCardId, studentId);
	}


	public boolean updateStudentReportCardStatusPayload(int instituteId,  UUID userId, StudentReportCardStatusPayload studentReportCardStatusPayload) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid institute id."));
		}
		if (userId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid user id"));
		}
		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.UPDATE_STUDENT_REPORT_CARD_STATUS);
		validateStudentReportCardStatusPayload(studentReportCardStatusPayload);
		return examinationDao.updateStudentReportCardStatusPayload(studentReportCardStatusPayload);
	}

	private void validateStudentReportCardStatusPayload(StudentReportCardStatusPayload studentReportCardStatusPayload) {
		if (studentReportCardStatusPayload.getInstituteId() <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid institute id."));
		}
		if (studentReportCardStatusPayload.getAcademicSessionId() <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid academic session id."));
		}
		if (studentReportCardStatusPayload.getReportCardId() == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid report card id."));
		}
		if (studentReportCardStatusPayload.getStudentExamDisplayDataStatus() == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid student exam display id."));
		}
		if (CollectionUtils.isEmpty(studentReportCardStatusPayload.getStudentIdSet())) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid student id set."));
		}
	}

	//	-----Student Report Card Status APIs-----

	public StandardReportCardTypePersonalityTraits getReportCardTypeAndPersonalityTraits(int instituteId, int academicSessionId, UUID standardId) {
		if (instituteId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
					"Invalid institute id."));
		}
		if (academicSessionId <= 0) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Invalid academic session id."));
		}
		if (standardId == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_STANDARD,
					"Invalid standard id."));
		}
		List<ExamReportCardMetadata> examReportCardMetadataList = getExamReportCardMetadatas(instituteId, academicSessionId, standardId);

		List<PersonalityTraitsDetails> personalityTraitsDetailsList = getStandardPersonalityTraits(instituteId,
				academicSessionId, standardId);

		ExaminationPreferences examinationPreferences = userPreferenceSettings.getExaminationPreferences(instituteId);

		if(CollectionUtils.isEmpty(examReportCardMetadataList) && CollectionUtils.isEmpty(personalityTraitsDetailsList) && examinationPreferences == null) {
			return null;
		}

		return new StandardReportCardTypePersonalityTraits(examReportCardMetadataList, personalityTraitsDetailsList, examinationPreferences);
	}
}
