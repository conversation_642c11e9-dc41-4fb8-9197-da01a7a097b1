package com.lernen.cloud.core.lib.student;

import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.institute.AcademicSession;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.report.DownloadFormat;
import com.lernen.cloud.core.api.report.ReportCellDetails;
import com.lernen.cloud.core.api.report.ReportDetails;
import com.lernen.cloud.core.api.report.ReportSheetDetails;
import com.lernen.cloud.core.api.student.*;
import com.lernen.cloud.core.api.student.StudentManagementReportsType;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.reports.ReportGenerator;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class StudentManagementReportsGenerator extends ReportGenerator {
    private static final Logger logger = LogManager.getLogger(StudentManagementReportsGenerator.class);

    private final StudentManager studentManager;
    private final UserPermissionManager userPermissionManager;
    private final InstituteManager instituteManager;

    public StudentManagementReportsGenerator(StudentManager studentManager, UserPermissionManager userPermissionManager,InstituteManager instituteManager){
        this.studentManager = studentManager;
        this.userPermissionManager = userPermissionManager;
        this.instituteManager = instituteManager;
    }


    public ReportDetails generateStudentReport(int instituteId, int academicSessionId, UUID userId, DownloadFormat downloadFormat, StudentManagementReportsType studentManagementReportsType) {

        if (instituteId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
                    "Institute id is invalid."));
        }
        if (academicSessionId <= 0) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_INSTITUTE,
                    "Academic Session id is invalid."));
        }

        if (userId == null) {
            throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_USER,
                    "User id cannot be null."));
        }

        if (downloadFormat == DownloadFormat.EXCEL) {
            if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STUDENT_MANAGEMENT_EXCEL_REPORT, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download student management excel reports!"));
            }
        } else if (downloadFormat == DownloadFormat.PDF) {
            if (!userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.STUDENT_MANAGEMENT_PDF_REPORT, false)) {
                throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                        "You don't have access to download student management pdf reports!"));
            }
        }

        switch (studentManagementReportsType){
            case CLASS_REPEATERS_REPORT:
                if (!userPermissionManager.verifyAuthorisation(instituteId, userId,
                        AuthorisationRequiredAction.GENERATE_CLASS_REPEATERS_REPORT, false)) {
                    throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.ACTION_NOT_AUTHORIZED,
                            "You don't have access to download class repeaters report!"));
                }
                return getClassRepeatersReport(instituteId,academicSessionId);
        }
        return null;
    }

    private ReportDetails getClassRepeatersReport(int institudeId, int academicSessionId){
        String reportName = "Class Repeaters";
        String sheetName = "ClassRepeatersReport";
        AcademicSession previousAcademicSession = instituteManager.getPreviousSessionDetails(institudeId,academicSessionId);
        if (previousAcademicSession == null) {
            logger.info("Empty report {} as no Previous Session Found", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
            return reportDetails;
        }
        int previousAcademicSessionId = previousAcademicSession.getAcademicSessionId();
        List<Student> repeaterStudents = studentManager.getClassRepeatersReport(institudeId,academicSessionId,previousAcademicSessionId);

        List<List<ReportCellDetails>> reportCellDetails = new ArrayList<List<ReportCellDetails>>();

        if (org.apache.commons.collections.CollectionUtils.isEmpty(repeaterStudents)) {
            logger.info("Empty report {}", reportName);
            List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();
            ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                    null);
            reportSheetDetailsList.add(reportSheetDetails);
            ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
            return reportDetails;
        }
        AcademicSession academicSession = null;
        if(academicSessionId != 0) {
            academicSession = instituteManager.getAcademicSessionByAcademicSessionId(academicSessionId);
        }

        List<ReportCellDetails> reportHeaderRow = new ArrayList<ReportCellDetails>();
        String heading = academicSession == null ? reportName : reportName + " (" + academicSession.getDisplayName() + ") ";
        reportHeaderRow.add(new ReportCellDetails(heading, STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));
        reportCellDetails.add(reportHeaderRow);

        List<ReportCellDetails> reportCellDetailsHeaderRow = new ArrayList<ReportCellDetails>();

        reportCellDetailsHeaderRow.add(new ReportCellDetails("S No.", STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));

        reportCellDetailsHeaderRow.add(new ReportCellDetails("Admission Number", STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));

        reportCellDetailsHeaderRow.add(new ReportCellDetails("Student Name", STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));

        reportCellDetailsHeaderRow.add(new ReportCellDetails("Father Name", STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));

        reportCellDetailsHeaderRow.add(new ReportCellDetails("Repeating Class", STRING,CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR,true));

        reportCellDetails.add(reportCellDetailsHeaderRow);

        int sNo=1;

        for(Student student: repeaterStudents){
            List<ReportCellDetails> reportCellDetailsRow = new ArrayList<>();

            reportCellDetailsRow.add(new ReportCellDetails(sNo++,
                    INTEGER, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            reportCellDetailsRow.add(new ReportCellDetails(StringUtils.isBlank(student.getStudentBasicInfo().getAdmissionNumber())?EMPTY_TEXT:student.getStudentBasicInfo().getAdmissionNumber(),STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            reportCellDetailsRow.add(new ReportCellDetails(StringUtils.isBlank(student.getStudentBasicInfo().getName())?EMPTY_TEXT:student.getStudentBasicInfo().getName(),STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            reportCellDetailsRow.add(new ReportCellDetails(StringUtils.isBlank(student.getStudentFamilyInfo().getFathersName())?EMPTY_TEXT:student.getStudentFamilyInfo().getFathersName(),STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            reportCellDetailsRow.add(new ReportCellDetails(student.getStudentAcademicSessionInfoResponse()==null?EMPTY_TEXT:student.getStudentAcademicSessionInfoResponse().getStandard().getDisplayNameWithSection(),STRING, CONTENT_SIZE, BLACK_COLOR, WHITE_COLOR, false));

            reportCellDetails.add(reportCellDetailsRow);
        }
        List<ReportSheetDetails> reportSheetDetailsList = new ArrayList<ReportSheetDetails>();

        ReportSheetDetails reportSheetDetails = new ReportSheetDetails(sheetName, null,
                reportCellDetails);
        reportSheetDetailsList.add(reportSheetDetails);
        ReportDetails reportDetails = new ReportDetails(reportName, reportSheetDetailsList);
        return reportDetails;
    }
}
