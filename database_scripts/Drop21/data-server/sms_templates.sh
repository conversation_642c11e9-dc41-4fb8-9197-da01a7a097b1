TEMPLATES_URL=$1/2.0/notification-templates/sms?user_id=20a4d8e0-3c28-4dcc-8960-96472455eb3b

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "entityName": "GLOBAL",
  "entityId": "global",
  "deliveryMode": "SMS",
  "templateType": "DUE_FEE",
  "templateValue": "Dear parents,\nTotal fee amount of ${due_fee_amount}/- is due for ${student_name}. Please clear dues in time to avoid penalties.\n\n-${institute_name}",
  "templateName": "Default Due Fee SMS",
  "locale": "en_US",
  "description": null
}' $TEMPLATES_URL

curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "entityName": "GLOBAL",
  "entityId": "global",
  "deliveryMode": "SMS",
  "templateType": "FEE_PAYMENT",
  "templateValue": "Dear <PERSON><PERSON>,\nPayment of amount ${total_fee_amount}/- collected for ${student_name} on ${payment_date}\n\nClass - ${class_name}\nInvoice# - ${invoice_id}\nCollected By - ${collected_by}\n\n${institute_name}",
  "templateName": "Default Fee Payment SMS",
  "locale": "en_US",
  "description": null
}' $TEMPLATES_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "entityName": "GLOBAL",
  "entityId": "global",
  "deliveryMode": "SMS",
  "templateType": "FEE_PAYMENT_CANCELLATION",
  "templateValue": "Dear Parent,\nYour bill with Invoice# - ${invoice_id} of amount ${total_fee_amount}/- for ${student_name} has been cancelled by ${cancelled_by} on ${payment_date}.\n\n${institute_name}",
  "templateName": "Default Fee Payment Cancellation SMS",
  "locale": "en_US",
  "description": null
}' $TEMPLATES_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "entityName": "GLOBAL",
  "entityId": "global",
  "deliveryMode": "SMS",
  "templateType": "FEE_PAYMENT",
  "templateValue": "Dear Parent,\nPayment of amount ${total_fee_amount}/- collected for ${student_name} on ${payment_date}\n\nClass - ${class_name}\nInvoice# - ${invoice_id}\n\n${institute_name}",
  "templateName": "Fee Payment SMS - Without Collected By",
  "locale": "en_US",
  "description": null
}' $TEMPLATES_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "entityName": "GLOBAL",
  "entityId": "global",
  "deliveryMode": "SMS",
  "templateType": "FEE_PAYMENT_CANCELLATION",
  "templateValue": "Dear Parent,\nYour bill with Invoice# - ${invoice_id} of amount ${total_fee_amount}/- for ${student_name} has been cancelled on ${payment_date}.\n\n${institute_name}",
  "templateName": "Fee Payment Cancellation SMS - Without Collected By",
  "locale": "en_US",
  "description": null
}' $TEMPLATES_URL


curl -H "Authorization: Bearer $2" -H "Content-Type:application/json" -X POST -d '{
  "entityName": "GLOBAL",
  "entityId": "global",
  "deliveryMode": "SMS",
  "templateType": "REGENERATE_CREDENTIALS",
  "templateValue": "${institute_name}\n\nCredentials for ${name}:\nID: ${user_id}\nPassword: ${user_password}\nAndroid: www.embrate.com/apk\nWeb portal: www.embrate.com/core/login",
  "templateName": "Default Reset Credentials SMS",
  "locale": "en_US",
  "description": null
}' $TEMPLATES_URL
