CREATE TABLE IF NOT EXISTS transport_staff(
	institute_id int NOT NULL,
	transport_staff_id varchar(36) PRIMARY KEY NOT NULL,
	staff_id varchar(225) NOT NULL,
	name varchar(1024) NOT NULL,
	transport_staff_type varchar(255) NOT NULL,
	joining_date int,
	age int DEFAULT NULL,
	experience varchar(255) DEFAULT NULL,
	aadhar_number varchar(255) DEFAULT NULL,
	contact_number varchar(255) DEFAULT NULL,
	UNIQUE KEY (institute_id, staff_id),
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);