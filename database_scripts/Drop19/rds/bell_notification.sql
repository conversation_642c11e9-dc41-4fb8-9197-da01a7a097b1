CREATE TABLE IF NOT EXISTS bell_notification_details(
	institute_id int NOT NULL,
	notification_id varchar(36) NOT NULL,
	user_id varchar(36) NOT NULL,
	title varchar(500) NOT NULL,
	body text,
	created_on TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	entity_name varchar(100) NOT NULL,
	entity_id varchar(36),
	list_opened_on TIMESTAMP NULL,
	clicked_on TIMESTAMP NULL,
	meta_data text,
	primary key(notification_id),
	unique key (notification_id, user_id),
	FOREIGN KEY (user_id) REFERENCES users(user_id)
);