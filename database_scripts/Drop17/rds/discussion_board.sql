CREATE TABLE IF NOT EXISTS channel_details(
	institute_id int NOT NULL,
	channel_id varchar(36) NOT NULL,
	entity varchar(100) NOT NULL,
	entity_id varchar(36),
	channel_name text NOT NULL,
	description text,
	created_by varchar(36),
	created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	user_type varchar(100) NOT NULL,
	primary key(channel_id),
	FOREIGN KEY (created_by) REFERENCES users(user_id),
    FOREIGN KEY (institute_id) REFERENCES institute(institute_id)
);

ALTER TABLE channel_details CHANGE channel_name channel_name text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE channel_details CHANGE description description text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE INDEX channel_details_entity_id ON channel_details (entity_id);


CREATE TABLE IF NOT EXISTS channel_threads(
	thread_id varchar(36) NOT NULL,
	channel_id varchar(36) NOT NULL,
	name text NOT NULL,
	description text,
	created_by varchar(36) NOT NULL,
	created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	attachments text,
	user_type varchar(100) NOT NULL,
	primary key(thread_id),
	FOREIGN KEY (channel_id) REFERENCES channel_details(channel_id),
	FOREIGN KEY (created_by) REFERENCES users(user_id)
);

ALTER TABLE channel_threads CHANGE name name text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE channel_threads CHANGE description description text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE INDEX channel_threads_channel_id ON channel_threads (channel_id);

CREATE TABLE IF NOT EXISTS conversation_details(
	conversation_id varchar(36) NOT NULL,
	thread_id varchar(36) NOT NULL,
	created_by varchar(36) NOT NULL,
	created_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
	updated_timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	message text NOT NULL,
	primary key(conversation_id),
	FOREIGN KEY (thread_id) REFERENCES channel_threads(thread_id),
	FOREIGN KEY (created_by) REFERENCES users(user_id)
);

ALTER TABLE conversation_details CHANGE message message text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE INDEX conversation_details_thread_id ON conversation_details (thread_id);

