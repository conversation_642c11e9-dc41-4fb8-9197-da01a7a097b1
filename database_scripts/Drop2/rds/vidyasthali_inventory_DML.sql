use lernen


insert into categories (institute_id,category_name,genders) values (10005,'stationery',0);
insert into categories (institute_id,category_name,genders) values (10005,'books',0);
insert into categories (institute_id,category_name) values (10005,'clothing');
insert into categories (institute_id,category_name) values (10005,'foot wear');
insert into categories (institute_id,category_name,genders,colors) values (10005,'note book',0,0);
insert into categories (institute_id,category_name) values (10005,'art & craft');
insert into categories (institute_id,category_name) values (10005,'personal care');
insert into categories (institute_id,category_name) values (10005,'bedding');
insert into categories (institute_id,category_name,genders,colors) values (10005,'energy drinks',0,0);
insert into categories (institute_id,category_name) values (10005,'accessories');



insert into colors (institute_id, color_name) values (10005,'maroon');
insert into colors (institute_id, color_name) values (10005,'black');
insert into colors (institute_id, color_name) values (10005,'brown');
insert into colors (institute_id, color_name) values (10005,'white');
insert into colors (institute_id, color_name) values (10005,'red');
insert into colors (institute_id, color_name) values (10005,'yellow');
insert into colors (institute_id, color_name) values (10005,'blue');
insert into colors (institute_id, color_name) values (10005,'navy blue');
insert into colors (institute_id, color_name) values (10005,'green');
insert into colors (institute_id, color_name) values (10005,'dark green');
insert into colors (institute_id, color_name) values (10005,'pink');
insert into colors (institute_id, color_name) values (10005,'purple');
insert into colors (institute_id, color_name) values (10005,'grey');
insert into colors (institute_id, color_name) values (10005,'olive');
insert into colors (institute_id, color_name) values (10005,'cyan');
insert into colors (institute_id, color_name) values (10005,'magenta');