CREATE TABLE IF NOT EXISTS user_attendance_log(
	institute_id varchar(36) NOT NULL,
	user_id varchar(36) NOT NULL,
	user_type varchar(36) NOT NULL,
	input_type varchar(36) NOT NULL,
	log_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	attendance_time timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
	logged_by varchar(36),
	metadata text
);


CREATE TABLE IF NOT EXISTS standard_attendance_type(
institute_id int NOT NULL,
attendance_type_id int NOT NULL,
standard_id varchar(36) NOT NULL,
metadata text,
PRIMARY KEY(institute_id, attendance_type_id, standard_id),
FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
FOREIGN KEY (attendance_type_id) REFERENCES attendance_types(attendance_type_id),
FOREIGN KEY (standard_id) REFERENCES standards(standard_id)
);

alter table attendance_types add column metadata text after description;

CREATE TABLE IF NOT EXISTS attendance_device(
	device_id varchar(36) NOT NULL,
	service_provider varchar(36) NOT NULL,
	ext_device_id varchar(64) NOT NULL,
	name varchar(64) NOT NULL,
	auth_token varchar(64) NOT NULL,
	auth_token_hash varchar(256) NOT NULL,
	status varchar(16) NOT NULL,
	metadata text,
	description varchar(512),
	PRIMARY KEY(device_id),
	UNIQUE KEY(service_provider, auth_token_hash)
);

CREATE TABLE IF NOT EXISTS institute_attendance_device(
	institute_id int NOT NULL,
	device_id varchar(36) NOT NULL,
	PRIMARY KEY(institute_id, device_id),
	FOREIGN KEY (institute_id) REFERENCES institute(institute_id),
	FOREIGN KEY (device_id) REFERENCES attendance_device(device_id)
);

ALTER TABLE user_attendance_log ADD added_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER logged_by;

CREATE INDEX user_id ON user_attendance_log (user_id);