insert into configuration values ("INSTITUTE", "702", "meta_data", "fees_sms_service_enabled", "true");
insert into configuration values ("INSTITUTE", "702", "meta_data", "institute_name_in_sms", "<PERSON><PERSON>h Vidya Mandir");
insert into configuration values ("INSTITUTE", "702", "sms_preferences", "buffer_sms_count", "0");
insert into configuration values ("INSTITUTE", "702", "sms_preferences", "fee_payment_sms_enabled", "true");
insert into configuration values ("INSTITUTE", "702", "sms_preferences", "fee_payment_cancellation_sms_enabled", "true");
insert into configuration values ("INSTITUTE", "702", "sms_preferences", "fee_payment_sms_text", "Dear Parent,\nPayment of amount ${total_fee_amount}/- collected for ${student_name} on ${payment_date}\n\nClass - ${class_name}\nInvoice# - ${invoice_id}\nCollected By - ${collected_by}\n\n${institute_name}");
insert into configuration values ("INSTITUTE", "702", "sms_preferences", "fee_payment_sms_student_name_format", "FIRST_NAME_ONLY");
insert into configuration values ("INSTITUTE", "702", "sms_preferences", "fee_payment_cancellation_sms_text", "Dear Parent,\nYour bill with Invoice# - ${invoice_id} of amount ${total_fee_amount}/- for ${student_name} has been cancelled by ${cancelled_by} on ${payment_date}.\n\n${institute_name}");
insert into configuration values ("INSTITUTE", "702", "sms_preferences", "fee_payment_cancellation_sms_student_name_format", "FIRST_NAME_ONLY");





insert into configuration values ("GLOBAL", "GLOBAL", "sms_preferences", "fee_payment_sms_text", "Dear Parent,\nPayment of amount ${total_fee_amount}/- collected for ${student_name} on ${payment_date}\n\nClass - ${class_name}\nInvoice# - ${invoice_id}\n\n${institute_name}");
insert into configuration values ("GLOBAL", "GLOBAL", "sms_preferences", "fee_payment_sms_student_name_format", "FULL_NAME");
insert into configuration values ("GLOBAL", "GLOBAL", "sms_preferences", "fee_payment_cancellation_sms_text", "Dear Parent,\nYour bill with Invoice# - ${invoice_id} of amount ${total_fee_amount}/- for ${student_name} has been cancelled on ${payment_date}.\n\n${institute_name}");
insert into configuration values ("GLOBAL", "GLOBAL", "sms_preferences", "fee_payment_cancellation_sms_student_name_format", "FULL_NAME");


insert into configuration values ("INSTITUTE", "10020", "sms_preferences", "buffer_sms_count", "0");
insert into counters values (10020, "SMS_COUNTER", 0, "");


insert into configuration values ("INSTITUTE", "10030", "meta_data", "fees_sms_service_enabled", "true");
insert into configuration values ("INSTITUTE", "10030", "meta_data", "institute_name_in_sms", "Adarsh Vidya Mandir");
insert into configuration values ("INSTITUTE", "10030", "sms_preferences", "buffer_sms_count", "0");
insert into configuration values ("INSTITUTE", "10030", "sms_preferences", "fee_payment_sms_enabled", "true");
insert into configuration values ("INSTITUTE", "10030", "sms_preferences", "fee_payment_cancellation_sms_enabled", "true");
insert into configuration values ("INSTITUTE", "10030", "sms_preferences", "fee_payment_sms_text", "Dear Parent,\nPayment of amount ${total_fee_amount}/- collected for ${student_name} on ${payment_date}\n\nClass - ${class_name}\nInvoice# - ${invoice_id}\nCollected By - ${collected_by}\n\n${institute_name}");
insert into configuration values ("INSTITUTE", "10030", "sms_preferences", "fee_payment_sms_student_name_format", "FIRST_NAME_ONLY");
insert into configuration values ("INSTITUTE", "10030", "sms_preferences", "fee_payment_cancellation_sms_text", "Dear Parent,\nYour bill with Invoice# - ${invoice_id} of amount ${total_fee_amount}/- for ${student_name} has been cancelled by ${cancelled_by} on ${payment_date}.\n\n${institute_name}");
insert into configuration values ("INSTITUTE", "10030", "sms_preferences", "fee_payment_cancellation_sms_student_name_format", "FIRST_NAME_ONLY");
insert into counters values (10030, "SMS_COUNTER", 0, "");


insert into configuration values ("INSTITUTE", "10031", "meta_data", "fees_sms_service_enabled", "true");
insert into configuration values ("INSTITUTE", "10031", "meta_data", "institute_name_in_sms", "Adarsh Inter College");
insert into configuration values ("INSTITUTE", "10031", "sms_preferences", "buffer_sms_count", "0");
insert into configuration values ("INSTITUTE", "10031", "sms_preferences", "fee_payment_sms_enabled", "true");
insert into configuration values ("INSTITUTE", "10031", "sms_preferences", "fee_payment_cancellation_sms_enabled", "true");
insert into configuration values ("INSTITUTE", "10031", "sms_preferences", "fee_payment_sms_text", "Dear Parent,\nPayment of amount ${total_fee_amount}/- collected for ${student_name} on ${payment_date}\n\nClass - ${class_name}\nInvoice# - ${invoice_id}\nCollected By - ${collected_by}\n\n${institute_name}");
insert into configuration values ("INSTITUTE", "10031", "sms_preferences", "fee_payment_sms_student_name_format", "FIRST_NAME_ONLY");
insert into configuration values ("INSTITUTE", "10031", "sms_preferences", "fee_payment_cancellation_sms_text", "Dear Parent,\nYour bill with Invoice# - ${invoice_id} of amount ${total_fee_amount}/- for ${student_name} has been cancelled by ${cancelled_by} on ${payment_date}.\n\n${institute_name}");
insert into configuration values ("INSTITUTE", "10031", "sms_preferences", "fee_payment_cancellation_sms_student_name_format", "FIRST_NAME_ONLY");
insert into counters values (10031, "SMS_COUNTER", 0, "");



insert into configuration values ("INSTITUTE", "10040", "meta_data", "institute_name_in_sms", "Golden Valley School");
insert into configuration values ("INSTITUTE", "10040", "sms_preferences", "buffer_sms_count", "0");
insert into counters values (10040, "SMS_COUNTER", 0, "");


insert into configuration values ("INSTITUTE", "10050", "meta_data", "institute_name_in_sms", "Golden Valley School");
insert into configuration values ("INSTITUTE", "10050", "sms_preferences", "buffer_sms_count", "0");
insert into counters values (10050, "SMS_COUNTER", 0, "");

insert into configuration values ("INSTITUTE", "10051", "meta_data", "institute_name_in_sms", "Golden Valley School");
insert into configuration values ("INSTITUTE", "10051", "sms_preferences", "buffer_sms_count", "0");
insert into counters values (10051, "SMS_COUNTER", 0, "");

