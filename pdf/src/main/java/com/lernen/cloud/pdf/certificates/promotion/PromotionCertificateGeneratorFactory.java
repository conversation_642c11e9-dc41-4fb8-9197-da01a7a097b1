/**
 * 
 */
package com.lernen.cloud.pdf.certificates.promotion;

import com.embrate.cloud.core.lib.utility.AssetProvider;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 *
 */
public class PromotionCertificateGeneratorFactory {

	private static final Map<Integer, PromotionCertificateGenerator> PROMOTION_CERTIFICATE_GENERATOR = new HashMap<>();
	private final AssetProvider assetProvider;

	public  PromotionCertificateGeneratorFactory(AssetProvider assetProvider) {
		this.assetProvider = assetProvider;
		initializeGenerators();
	}

	private void initializeGenerators(){
		PROMOTION_CERTIFICATE_GENERATOR.put(10005, new PromotionCertificateGenerator_10005(assetProvider));
		PROMOTION_CERTIFICATE_GENERATOR.put(10006, new PromotionCertificateGenerator_10005(assetProvider));
		PROMOTION_CERTIFICATE_GENERATOR.put(10190, new PromotionCertificateGenerator_10190(assetProvider));
	}

	public PromotionCertificateGenerator getPromotionCertificateGenerator(int instituteId) {
		if (!PROMOTION_CERTIFICATE_GENERATOR.containsKey(instituteId)) {
			return new GlobalPromotionCertificateGenerator(assetProvider);
		}
		return PROMOTION_CERTIFICATE_GENERATOR.get(instituteId);
	}

}
