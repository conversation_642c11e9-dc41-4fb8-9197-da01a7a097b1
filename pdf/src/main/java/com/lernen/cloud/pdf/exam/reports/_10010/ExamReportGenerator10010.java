package com.lernen.cloud.pdf.exam.reports._10010;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.itextpdf.kernel.colors.Color;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.TextAlignment;
import com.lernen.cloud.core.api.course.CourseType;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.examination.report.ExamReportCardLayoutData;
import com.lernen.cloud.core.api.examination.report.ExamReportCourseMarksRow;
import com.lernen.cloud.core.api.examination.report.ExamReportData;
import com.lernen.cloud.core.api.examination.report.configs.GridConfigs;
import com.lernen.cloud.core.api.examination.report.ExamResultStatus;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.StandardSections;
import com.lernen.cloud.core.api.pdf.CellData;
import com.lernen.cloud.core.api.pdf.CellLayoutSetup;
import com.lernen.cloud.core.api.pdf.DocumentLayoutSetup;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentLite;
import com.lernen.cloud.core.api.user.User;
import com.lernen.cloud.core.lib.student.StudentManager;
import com.lernen.cloud.core.utils.DateUtils;
import com.lernen.cloud.core.utils.images.LogoProvider;
import com.lernen.cloud.pdf.exam.reports.ExamReportGenerator;
import com.lernen.cloud.pdf.exam.reports.IExamReportCardGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 */
public class ExamReportGenerator10010 extends ExamReportGenerator implements IExamReportCardGenerator {
    public ExamReportGenerator10010(AssetProvider assetProvider) {
                super(assetProvider);
                //TODO Auto-generated constructor stub
        }

public static final float DEFAULT_PAGE_SIDE_MARGIN = 25f;
    public static final float DEFAULT_PAGE_TOP_MARGIN = 10f;
    public static final float DEFAULT_PAGE_BOTTOM_MARGIN = 10f;
    public static final float SQUARE_BORDER_MARGIN = 12f;
    protected static final String HALF_YEARLY_REPORT_TYPE = "HALF_YEARLY";
    protected static final String ANNUAL_REPORT_TYPE = "ANNUAL";
    protected static final String SCHOLASTIC_EXAM_DESCRIPTION = "MM : Max Marks, MO : Marks Obtained";
    private static final Logger logger = LogManager.getLogger(ExamReportGenerator10010.class);

//	Red : rgb(204, 0, 0)
//	Purple : rgb(57, 65, 158)
//	Blue : rgb(1, 155, 248)
    private static final String GRADE_A1 = "Outstanding performance and has extraordinary thinking.";
    private static final String GRADE_A2 = "Excellent effort, follow deadlines and maintain decency.";
    private static final String GRADE_B1 = "Gracefully takes the task and has tendency to do better.";
    private static final String GRADE_B2 = "Well behaved, has the capacity to work hard in order to reach heights.";
    private static final String GRADE_C1 = "Well behaved, has the capacity to work hard in order to reach heights.";
    private static final String GRADE_C2 = "Average performance but innovative.";
    private static final String GRADE_D = "Needs to get serious towards studies.";
    private static final String GRADE_E = "Needs to be very attentive and work hard in order to get promoted.";

    private static final Map<String, String> MARKS_GRADE_MAP = new LinkedHashMap<>();

    static {
        MARKS_GRADE_MAP.put("91 - 100", "A1");
        MARKS_GRADE_MAP.put("81 - 90", "A2");
        MARKS_GRADE_MAP.put("71 - 80", "B1");
        MARKS_GRADE_MAP.put("61 - 70", "B2");
        MARKS_GRADE_MAP.put("51 - 60", "C1");
        MARKS_GRADE_MAP.put("41 - 50", "C2");
        MARKS_GRADE_MAP.put("33 - 40", "D");
        MARKS_GRADE_MAP.put("32 & Below", "E(Failed)");
    }

    @Override
    public DocumentOutput generateReport(Institute institute, Student student, String reportType,
                                         ExamReportData examReportData, String documentName, StudentManager studentManager) {
        try {

            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());

            ExamReportCardLayoutData examReportCardLayoutData = generateReportCardLayoutData(institute, documentOutput);

            generateMetaDataLayout(examReportCardLayoutData, institute, examReportData.getStudentLite(), studentManager, reportType,
                    examReportData);

            generateScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                    examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Scholastic Subjects",
                    getScholasticMarksGridSubjectWidth(reportType), null, "#39419e0");

            generateCoScholasticMarksGrid(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                    examReportCardLayoutData.getDefaultBorderWidth(), examReportData, GridConfigs.forSkipTotalRow(), "Co-Scholastic Subjects",
                    getCoScholasticMarksGridSubjectWidth(reportType));

            generateResultSummary(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                    examReportData, reportType);

            generateSignatureBox(examReportCardLayoutData.getDocument(),
                    examReportCardLayoutData.getDocumentLayoutSetup(), examReportCardLayoutData.getContentFontSize(),
                    examReportCardLayoutData.getDefaultBorderWidth(), 1);

            examReportCardLayoutData.getDocument().close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, student {}, reportType {} ",
                    institute.getInstituteId(), student.getStudentId(), reportType, e);
        }
        return null;
    }

    protected float getScholasticMarksGridSubjectWidth(String reportType) {
        if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
            return 0.2f;
        }
        return 0.2f;
    }

    protected float getCoScholasticMarksGridSubjectWidth(String reportType) {
        if (reportType.equalsIgnoreCase(HALF_YEARLY_REPORT_TYPE)) {
            return 0.5f;
        }
        if (reportType.equalsIgnoreCase(ANNUAL_REPORT_TYPE)) {
            return 0.4f;
        }
        return 0.3f;
    }

    protected ExamReportCardLayoutData generateReportCardLayoutData(Institute institute, DocumentOutput documentOutput)
            throws IOException {

        DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(false);
        float contentFontSize = 12f;
        float defaultBorderWidth = 0.5f;
        Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);
        float logoWidth = 75f;
        float logoHeight = 75f;

        int instituteId = institute.getInstituteId();
        return new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, contentFontSize,
                defaultBorderWidth, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));
    }

    protected void generateMetaDataLayout(ExamReportCardLayoutData examReportCardLayoutData, Institute institute,
                                          StudentLite studentLite, StudentManager studentManager, String reportType, ExamReportData examReportData) throws IOException {

        generateHeader(examReportCardLayoutData, studentLite, institute, reportType, 40f,
                examReportCardLayoutData.getDocumentLayoutSetup().getPageSize().getHeight() * 0.87f);
        generateStudentInformation(examReportCardLayoutData, studentLite, examReportData);
        generateBorderLayout(examReportCardLayoutData);
    }

    @Override
    public DocumentLayoutSetup initDocumentLayoutSetup(boolean officeCopy) {
        return initDocumentLayoutSetup(officeCopy, officeCopy ? DEFAULT_PAGE_SIZE.rotate() : DEFAULT_PAGE_SIZE,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_TOP_MARGIN : DEFAULT_PAGE_TOP_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_BOTTOM_MARGIN : DEFAULT_PAGE_BOTTOM_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_PAGE_SIDE_MARGIN : DEFAULT_PAGE_SIDE_MARGIN,
                officeCopy ? DEFAULT_DOUBLE_CONTENT_MIDDLE_MARGIN : 0f);
    }

    protected void generateHeader(ExamReportCardLayoutData examReportCardLayoutData, StudentLite studentLite,
                                  Institute institute, String reportType, float offsetX, float offsetY) throws IOException {

        generateDynamicImageProvider(examReportCardLayoutData, offsetX, offsetY, institute.getInstituteId(), InstituteDocumentType.INSTITUTE_PRIMARY_LOGO);

        int singleContentColumn = 1;
        Table table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(14f).setTextAlignment(TextAlignment.CENTER);

        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getInstituteName())
                        .setFontColor(Color.convertRgbToCmyk(new DeviceRgb(57, 65, 158)))),
                cellLayoutSetup.copy().setFontSize(22f));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine1())
                        .setFontColor(Color.convertRgbToCmyk(new DeviceRgb(204, 0, 0)))),
                cellLayoutSetup.copy().setFontSize(13f));
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(institute.getLetterHeadLine2())
                        .setFontColor(Color.convertRgbToCmyk(new DeviceRgb(204, 0, 0)))),
                cellLayoutSetup.copy().setFontSize(13f).setPdfFont(getRegularFont()));

        examReportCardLayoutData.getDocument().add(table);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 2);
        table = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), singleContentColumn);

        String headerExamTitle = "PROGRESS REPORT ";
        headerExamTitle += "(" + studentLite.getStudentSessionData().getShortYearDisplayName() + ")";
        addRow(table, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(getParagraph(headerExamTitle).setUnderline()),
                cellLayoutSetup.copy().setFontSize(12f).setPdfFont(getRegularBoldFont()));
        examReportCardLayoutData.getDocument().add(table);

        addBlankLine(examReportCardLayoutData.getDocument(), false, 2);

    }

    protected void generateStudentInformation(ExamReportCardLayoutData examReportCardLayoutData,
                                              StudentLite studentLite, ExamReportData examReportData) throws IOException {

        PdfFont boldFont = getRegularBoldFont();
        Document document = examReportCardLayoutData.getDocument();
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        float contentFontSize = examReportCardLayoutData.getContentFontSize();

        Table table = getPDFTable(documentLayoutSetup, new float[]{0.4f, 0.2f, 0.4f});

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(boldFont).setFontSize(contentFontSize);

        CellLayoutSetup firstCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);
        CellLayoutSetup secondCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.RIGHT);
        CellLayoutSetup thirdCellLayoutSetup = cellLayoutSetup.copy().setTextAlignment(TextAlignment.LEFT);

        Paragraph studentName = getKeyValueParagraph("Student Name : ", studentLite.getName(), 57, 65, 158, boldFont, boldFont);
        Paragraph fatherName = getKeyValueParagraph("Father Name : ", studentLite.getFathersName(), 57, 65, 158, boldFont, boldFont);
        Paragraph dob = getKeyValueParagraph("DOB : ",
                studentLite.getDateOfBirth() == null || studentLite.getDateOfBirth() <= 0 ? ""
                        : DateUtils.getFormattedDate(studentLite.getDateOfBirth(), DATE_FORMAT, User.DFAULT_TIMEZONE), 57, 65, 158, boldFont, boldFont);
        Paragraph motherName = getKeyValueParagraph("Mother Name : ", studentLite.getMothersName(), 57, 65, 158, boldFont, boldFont);
        Paragraph admissionNumber = getKeyValueParagraph("Admission No. : ", studentLite.getAdmissionNumber(), 57, 65, 158, boldFont, boldFont);
        Paragraph classValue = getKeyValueParagraph("Class : ",
                studentLite.getStudentSessionData().getStandardNameWithSection(), 57, 65, 158, boldFont, boldFont);
        Paragraph rollNumber = getKeyValueParagraph("Roll Number : ", studentLite.getStudentSessionData().getRollNumber(), 57, 65, 158, boldFont, boldFont);
        Paragraph dateOfResultDeclaration = getKeyValueParagraph("Date Of Result Declaration : ",
                examReportData.getDateOfResultDeclaration() == null ? "-" : DateUtils.getFormattedDate(examReportData.getDateOfResultDeclaration()),
                57, 65, 158, boldFont, boldFont);


        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(studentName, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(fatherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(dob, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(motherName, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(admissionNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(classValue, thirdCellLayoutSetup)));
        addRow(table, documentLayoutSetup,
                Arrays.asList(new CellData(rollNumber, firstCellLayoutSetup), new CellData("", secondCellLayoutSetup),
                        new CellData(dateOfResultDeclaration, thirdCellLayoutSetup)));

        document.add(table);

        addBlankLine(document, false, 1);

    }

    protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData) {
        generateBorderLayout(examReportCardLayoutData, 1);
    }

    protected void generateBorderLayout(ExamReportCardLayoutData examReportCardLayoutData, int pageNumber) {
        DocumentLayoutSetup documentLayoutSetup = examReportCardLayoutData.getDocumentLayoutSetup();
        PdfCanvas canvas = new PdfCanvas(examReportCardLayoutData.getDocument().getPdfDocument(), pageNumber);
        canvas.rectangle(SQUARE_BORDER_MARGIN - 2, SQUARE_BORDER_MARGIN - 2,
                documentLayoutSetup.getPageSize().getWidth() - (SQUARE_BORDER_MARGIN - 2) * 2,
                documentLayoutSetup.getPageSize().getHeight() - (SQUARE_BORDER_MARGIN - 2) * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
        canvas.rectangle(SQUARE_BORDER_MARGIN, SQUARE_BORDER_MARGIN,
                documentLayoutSetup.getPageSize().getWidth() - SQUARE_BORDER_MARGIN * 2,
                documentLayoutSetup.getPageSize().getHeight() - SQUARE_BORDER_MARGIN * 2);
        canvas.stroke();
        canvas.setLineWidth(1f);
    }

    protected void generateResultSummary(Document document, DocumentLayoutSetup documentLayoutSetup,
                                         float contentFontSize, ExamReportData examReportData, String reportType) throws IOException {

        PdfFont boldFont = getRegularBoldFont();

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.LEFT).setPaddingTop(2f).setPaddingBottom(2f);

        Table table = getPDFTable(documentLayoutSetup, new float[]{0.4f, 0.2f, 0.4f});

        Paragraph noOfMeetings = getKeyValueParagraph("Number of Meetings : ", examReportData.getTotalWorkingDays() == null ? ""
                : String.valueOf(examReportData.getTotalWorkingDays()), 1, 155, 248, boldFont, boldFont);
        Paragraph noOfPresent = getKeyValueParagraph("Number of presents : ", examReportData.getTotalAttendedDays() == null ? ""
                : String.valueOf(examReportData.getTotalAttendedDays()), 1, 155, 248, boldFont, boldFont);
        Paragraph result = examReportData.getExamResultStatus() == null ? getKeyValueParagraph("Result : ", "-", boldFont) :
                examReportData.getExamResultStatus() == ExamResultStatus.PASS ? getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().name(),
                        76, 142, 48, boldFont, boldFont) : getKeyValueParagraph("Result : ", examReportData.getExamResultStatus().name(),
                        255, 0, 0, boldFont, boldFont);
        Paragraph promotedClass = getKeyValueParagraph("Promoted to: ",
                examReportData.getPromotedTo() == null ? "-" : examReportData.getPromotedTo().getStandardName(), 57, 65, 158, boldFont, boldFont);
        Paragraph obtainedMarks = getKeyValueParagraph("Obtained marks : ",
                examReportData.getTotalObtainedMarks() == null ? "-"
                        : examReportData.getTotalObtainedMarks() * 10 % 10 == 0
                        ? String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10)
                        : String.valueOf(Math.round(examReportData.getTotalObtainedMarks() * 10) / 10d), 57, 65, 158, boldFont, boldFont);
        Paragraph totalMarks = getKeyValueParagraph("Total marks : ", examReportData.getTotalMaxMarks() == null ? "-"
                : String.valueOf(Math.round(examReportData.getTotalMaxMarks() * 10) / 10), 57, 65, 158, boldFont, boldFont);
        Paragraph percentage = getKeyValueParagraph("Percentage : ", examReportData.getPercentage() == null ? "-"
                : String.valueOf(Math.round(examReportData.getPercentage() * 100) / 100d) + "%", 57, 65, 158, boldFont, boldFont);
        Paragraph grade = getKeyValueParagraph("Grade : ",
                examReportData.getTotalGrade() == null ? "-" : examReportData.getTotalGrade().getGradeName(), 57, 65, 158, boldFont, boldFont);

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(noOfMeetings, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(noOfPresent, cellLayoutSetup)));

        if (reportType.equalsIgnoreCase("ANNUAL")) {
            addRow(table, documentLayoutSetup, Arrays.asList(new CellData(result, cellLayoutSetup),
                    new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(promotedClass, cellLayoutSetup)));
        }

        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(obtainedMarks, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(totalMarks, cellLayoutSetup)));
        addRow(table, documentLayoutSetup, Arrays.asList(new CellData(percentage, cellLayoutSetup),
                new CellData(EMPTY_TEXT, cellLayoutSetup), new CellData(grade, cellLayoutSetup)));

        document.add(table);

    }

    protected void generateRemarksSection(ExamReportCardLayoutData examReportCardLayoutData,
                                          ExamReportData examReportData) throws IOException {

        CellLayoutSetup cellLayoutSetup = new CellLayoutSetup();
        cellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(examReportCardLayoutData.getContentFontSize())
                .setTextAlignment(TextAlignment.LEFT);

        Table remarksTable = getPDFTable(examReportCardLayoutData.getDocumentLayoutSetup(), 1);

        Paragraph remarks = getKeyValueParagraph("Remarks: ",
                examReportData.getTotalGrade() == null ? "" :
                        StringUtils.isEmpty(getRemarks(examReportData.getTotalGrade().getGradeName()))
                                ? "" : getRemarks(examReportData.getTotalGrade().getGradeName()));

//
//		Paragraph remarks = getKeyValueParagraph("Remark : ",
//				examReportData.getRemarks() == null ? "" : examReportData.getRemarks());
        addRow(remarksTable, examReportCardLayoutData.getDocumentLayoutSetup(), Arrays.asList(remarks),
                cellLayoutSetup);
        examReportCardLayoutData.getDocument().add(remarksTable);
    }

    protected void generateSignatureBox(Document document, DocumentLayoutSetup documentLayoutSetup,
                                        float contentFontSize, float defaultBorderWidth, int pageNumber) throws IOException {
        PdfCanvas canvas = new PdfCanvas(document.getPdfDocument(), pageNumber);
        canvas.moveTo(12, 100);
        canvas.lineTo(583, 100);
        canvas.setLineWidth(.5f);
        canvas.closePathStroke();
        int singleContentColumn = 3;
        Table table = getPDFTable(documentLayoutSetup, singleContentColumn);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize)
                .setTextAlignment(TextAlignment.CENTER);

        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("Class Teacher"), getParagraph("Exam Incharge"),
                getParagraph("Principal")), signatureCellLayoutSetup);
        table.setFixedPosition(30f, 100f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(table);
        generateGradeBox(document, documentLayoutSetup, contentFontSize, defaultBorderWidth);
    }

    private void generateGradeBox(Document document, DocumentLayoutSetup documentLayoutSetup, float contentFontSize,
                                  float defaultBorderWidth) throws IOException {

        Table table = getPDFTable(documentLayoutSetup, 1);
        CellLayoutSetup signatureCellLayoutSetup = new CellLayoutSetup();
        signatureCellLayoutSetup.setPdfFont(getRegularBoldFont()).setFontSize(contentFontSize - 2)
                .setTextAlignment(TextAlignment.CENTER);
        addRow(table, documentLayoutSetup, Arrays.asList(getParagraph("MARKS RANGE")), signatureCellLayoutSetup);
        table.setFixedPosition(10f, 80f, documentLayoutSetup.getPageSize().getWidth());
        document.add(table);

        float[] columnWidths = new float[]{0.24f, 0.24f, 0.24f, 0.24f};
        Table headerTable = getPDFTable(documentLayoutSetup, columnWidths);
        CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
        marksCellLayoutSetup.setPdfFont(getRegularFont()).setFontSize(contentFontSize - 4)
                .setBorder(new SolidBorder(defaultBorderWidth)).setTextAlignment(TextAlignment.CENTER);

        List<Map.Entry<String, String>> gradesList = new ArrayList<>(MARKS_GRADE_MAP.entrySet());
        for (int index = 0; index < gradesList.size() / 2; index++) {
            List<CellData> row = new ArrayList<>();
            row.add(new CellData(gradesList.get(index).getKey(), marksCellLayoutSetup));
            row.add(new CellData(gradesList.get(index).getValue(), marksCellLayoutSetup));
            row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getKey(), marksCellLayoutSetup));
            row.add(new CellData(gradesList.get(gradesList.size() / 2 + index).getValue(), marksCellLayoutSetup));
            addRow(headerTable, documentLayoutSetup, row);
        }
        headerTable.setFixedPosition(35f, 15f, documentLayoutSetup.getPageSize().getWidth() - 100f);
        document.add(headerTable);
    }

    protected Set<UUID> getNonAdditionalSubjects(ExamReportData examReportData) {
        Set<UUID> nonAdditionalSubjects = new HashSet<UUID>();
        for (ExamReportCourseMarksRow examReportCourseMarksRow : examReportData.getCourseTypeExamReportMarksGrid()
                .get(CourseType.SCHOLASTIC).getExamReportCourseMarksRows()) {
            if (!isAdditionalSubject(examReportCourseMarksRow.getCourse(), examReportData.getExamReportStructure())) {
                nonAdditionalSubjects.add(examReportCourseMarksRow.getCourse().getCourseId());
            }
        }
        return nonAdditionalSubjects;
    }

    protected void sortClassExamReports(List<ExamReportData> examReportDataList) {
        Collections.sort(examReportDataList, new Comparator<ExamReportData>() {

            @Override
            public int compare(ExamReportData e1, ExamReportData e2) {
                StandardSections section1 = e1.getStudentLite().getStudentSessionData().getStandardSection();

                StandardSections section2 = e2.getStudentLite().getStudentSessionData().getStandardSection();

                if (section1 != null && section2 != null) {
                    int sectionCompare = section1.getSectionName().compareToIgnoreCase(section2.getSectionName());
                    if (sectionCompare != 0) {
                        return sectionCompare;
                    }
                }

                return e1.getStudentLite().getName().compareToIgnoreCase(e2.getStudentLite().getName());
            }
        });
    }

    @Override
    public DocumentOutput generateClassReport(Institute institute, String reportType,
                                                    List<ExamReportData> examReportDataList, String documentName, StudentManager studentManager) {

        return null;
    }

    private String getRemarks(String grade) {
        switch (grade) {

            case "A1":
                return GRADE_A1;
            case "A2":
                return GRADE_A2;
            case "B1":
                return GRADE_B1;
            case "B2":
                return GRADE_B2;
            case "C1":
                return GRADE_C1;
            case "C2":
                return GRADE_C2;
            case "D":
                return GRADE_D;
            case "E":
                return GRADE_E;
            default:
                return null;

        }
    }

    @Override
    public DocumentOutput generateClassResultReport(Institute institute, String reportType, List<ExamReportData> examReportDataList,
                                                    String documentName, StudentManager studentManager, int studentPerPage) {
        try {
            DocumentOutput documentOutput = new DocumentOutput(documentName, new ByteArrayOutputStream());
            DocumentLayoutSetup documentLayoutSetup = initDocumentLayoutSetup(PageSize.A4);
            Document document = initDocument(documentOutput.getContent(), documentLayoutSetup);

            float logoWidth = 75f;
            float logoHeight = 75f;
            PdfFont boldFont = getRegularBoldFont();
            PdfFont regularFont = getRegularFont();
            int instituteId = institute.getInstituteId();
            ExamReportCardLayoutData examReportCardLayoutData = new ExamReportCardLayoutData(document, documentLayoutSetup, null, null, DEFAULT_FONT_SIZE,
                    DEFAULT_BORDER_WIDTH, logoWidth, logoHeight, LogoProvider.INSTANCE.getLogo(instituteId));

            CellLayoutSetup marksCellLayoutSetup = new CellLayoutSetup();
            marksCellLayoutSetup.setPdfFont(regularFont).setFontSize(DEFAULT_FONT_SIZE)
                    .setBorder(new SolidBorder(DEFAULT_BORDER_WIDTH)).setTextAlignment(TextAlignment.CENTER);

            CellLayoutSetup courseCellLayoutSetup = marksCellLayoutSetup.copy().setPdfFont(boldFont)
                    .setTextAlignment(TextAlignment.LEFT);

            String instituteName = institute.getInstituteName().toUpperCase();
            String sessionName = "";
            if(!CollectionUtils.isEmpty(examReportDataList)) {
                sessionName = "Session : " + examReportDataList.get(0).getStudentLite().getStudentSessionData().getShortYearDisplayName();
            }
            generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName,
                    sessionName, marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));

            sortClassExamReports(examReportDataList);
            int pageNumber = 1;
            for (ExamReportData examReportData : examReportDataList) {
                generateClassReportDataStudentDetails(documentLayoutSetup, document, courseCellLayoutSetup, examReportData.getStudentLite());
                generateScholasticMarksGrid(document,
                        documentLayoutSetup, DEFAULT_FONT_SIZE, DEFAULT_BORDER_WIDTH,
                        examReportData, GridConfigs.forOnlyObtainedTotalRow(), "Scholastic Subjects",
                        getScholasticMarksGridSubjectWidth(reportType), null, "#39419e0");
                generateClassReportDataStudentResult(documentLayoutSetup, document, examReportData, courseCellLayoutSetup);
                if (pageNumber % studentPerPage == 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                    generateClassReportDataPageHeader(documentLayoutSetup, document, boldFont, instituteName, sessionName,
                            marksCellLayoutSetup.copy().setBorder(null).setPdfFont(boldFont));
                } else {
                    addBlankLine(document, false, 1);
                }
                pageNumber++;
            }
            document.close();
            return documentOutput;
        } catch (Exception e) {
            logger.error("Error while generating report card institute {}, reportType {} ", institute.getInstituteId(),
                    reportType, e);
        }
        return null;
    }

    protected void generateScholasticExamDescription(Document document, DocumentLayoutSetup documentLayoutSetup,
                                                     float contentFontSize, ExamReportData examReportData, PdfFont regularFont, PdfFont boldFont) throws IOException {
        Text descTitle = new Text("Description: ").setFont(boldFont).setFontSize(contentFontSize - 2);

        Text descText = new Text(SCHOLASTIC_EXAM_DESCRIPTION)
                .setFontSize(contentFontSize - 4);
        Paragraph desc = new Paragraph();
        desc.add(descTitle).add(descText);
        document.add(desc);
    }
}
