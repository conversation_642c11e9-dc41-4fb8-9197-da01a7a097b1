package com.embrate.cloud.pdf.fees;

import com.embrate.cloud.core.lib.utility.AssetProvider;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.permissions.AuthorisationRequiredAction;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.lib.institute.InstituteManager;
import com.lernen.cloud.core.lib.permissions.UserPermissionManager;
import com.lernen.cloud.core.lib.student.StudentManager;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.*;

public class FeeChallaHandler {
	private static final Logger logger = LogManager.getLogger(FeeChallaHandler.class);

	private final FeeChallanGeneratorFactory feeChallanGeneratorFactory;
	private final InstituteManager instituteManager;
	private final StudentManager studentManager;
	private final UserPermissionManager userPermissionManager;

	public FeeChallaHandler(InstituteManager instituteManager, StudentManager studentManager,
							UserPermissionManager userPermissionManager, AssetProvider assetProvider) {
		this.feeChallanGeneratorFactory = new FeeChallanGeneratorFactory(assetProvider);
		this.instituteManager = instituteManager;
		this.studentManager = studentManager;
		this.userPermissionManager = userPermissionManager;
	}

	public DocumentOutput generateChallanDocument(int instituteId, int academicSessionId, UUID studentId, UUID userId) {
		if (instituteId <= 0 || academicSessionId <= 0 || studentId == null || userId == null) {
			logger.error("Invalid institute id or academicSessionId or studentId");
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_DETAILS,
					"Invalid institute id or academicSessionId or studentId"));
		}

		userPermissionManager.verifyAuthorisation(instituteId, userId, AuthorisationRequiredAction.GENERATE_FEE_CHALLAN);

		Student student = studentManager.getStudentByAcademicSessionStudentId(instituteId, academicSessionId,
				studentId);

		if (student == null) {
			logger.info(
					"No student found in instituteId {}, session {}, studentId {}. Skipping idenity card generation.",
					instituteId, academicSessionId, studentId);
			return null;
		}
		FeeChallanGenerator feeChllanGenerator = feeChallanGeneratorFactory
				.getfeeChallanGenerator(instituteId);

		String fileName = student.getStudentBasicInfo().getName() + " - Challan" + ".pdf";
		return feeChllanGenerator.generateFeeChallan(studentManager, instituteManager.getInstitute(instituteId), student, fileName);
	}
}
