package com.embrate.cloud.pdf.boardregistrationform;

import com.embrate.cloud.core.lib.utility.AssetProvider;

import java.util.HashMap;
import java.util.Map;

public class BoardRegistrationFormGeneratorFactory {

	private static final Map<Integer, BoardRegistrationFormGenerator> INSTITUTE_BOARD_REGISTRATION_FORM_GENERATOR = new HashMap<>();
	private final AssetProvider assetProvider;

	public BoardRegistrationFormGeneratorFactory(AssetProvider assetProvider) {
		this.assetProvider = assetProvider;
		initializeGenerators();
	}

	private void initializeGenerators() {
		INSTITUTE_BOARD_REGISTRATION_FORM_GENERATOR.put(10130, new BoardRegistrationFormGenerator10130(assetProvider));
	}

    public BoardRegistrationFormGenerator getRegistrationFormGenerator(int instituteId) {

		if (!INSTITUTE_BOARD_REGISTRATION_FORM_GENERATOR.contains<PERSON><PERSON>(instituteId)) {
			return new GlobalBoardRegistrationFormGenerator(assetProvider);
		}
		return INSTITUTE_BOARD_REGISTRATION_FORM_GENERATOR.get(instituteId);
	}
}
