<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:oauth="http://www.springframework.org/schema/security/oauth2"
       xmlns:sec="http://www.springframework.org/schema/security"

       xsi:schemaLocation="http://www.springframework.org/schema/beans
                      http://www.springframework.org/schema/beans/spring-beans.xsd
					  http://www.springframework.org/schema/security/oauth2
					  http://www.springframework.org/schema/security/spring-security-oauth2.xsd
					  http://www.springframework.org/schema/security
					  http://www.springframework.org/schema/security/spring-security.xsd">

    <import resource="classpath:core-utils.xml"/>

    <oauth:authorization-server
            client-details-service-ref="clientDetails"
            token-services-ref="tokenServices" authorization-request-manager-ref="authorizationRequestManager">
        <oauth:refresh-token/>
        <oauth:client-credentials/>
    </oauth:authorization-server>


    <oauth:resource-server id="resourceServerFilter"
                           token-services-ref="tokenServices"/>

    <authentication-manager alias="clientAuthenticationManager"
                            xmlns="http://www.springframework.org/schema/security">
        <authentication-provider user-service-ref="clientDetailsUserService">
            <!--            <password-encoder ref="passwordEncoder" />-->
        </authentication-provider>
    </authentication-manager>

    <!-- REST API Endpoints -->
    <!--    https://github.com/spring-projects/spring-security-oauth/issues/625-->
    <http pattern="/oauth/token" create-session="stateless"
          authentication-manager-ref="clientAuthenticationManager"
          xmlns="http://www.springframework.org/schema/security">
        <!--        <intercept-url pattern="/oauth/token" access="fullyAuthenticated" />-->
        <intercept-url pattern="/oauth/token" access="IS_AUTHENTICATED_FULLY"/>
        <anonymous enabled="false"/>
        <http-basic entry-point-ref="clientAuthenticationEntryPoint"/>
        <access-denied-handler ref="oauthAccessDeniedHandler"/>
    </http>

    <http pattern="/oauth/invalidate" create-session="stateless"
          authentication-manager-ref="clientAuthenticationManager"
          xmlns="http://www.springframework.org/schema/security">
        <intercept-url pattern="/oauth/invalidate" access="IS_AUTHENTICATED_FULLY"/>
        <anonymous enabled="false"/>
        <http-basic entry-point-ref="clientAuthenticationEntryPoint"/>
        <access-denied-handler ref="oauthAccessDeniedHandler"/>
    </http>

    <http pattern="/oauth/validate" create-session="stateless"
          authentication-manager-ref="clientAuthenticationManager"
          xmlns="http://www.springframework.org/schema/security">
        <intercept-url pattern="/oauth/validate" access="IS_AUTHENTICATED_FULLY"/>
        <anonymous enabled="false"/>
        <http-basic entry-point-ref="clientAuthenticationEntryPoint"/>
        <access-denied-handler ref="oauthAccessDeniedHandler"/>
    </http>

    <http pattern="/oauth2/token" create-session="stateless"
          authentication-manager-ref="clientAuthenticationManager"
          xmlns="http://www.springframework.org/schema/security">
        <intercept-url pattern="/oauth2/token" access="IS_AUTHENTICATED_FULLY"/>
        <anonymous enabled="false"/>
        <http-basic entry-point-ref="clientAuthenticationEntryPoint"/>
        <access-denied-handler ref="oauthAccessDeniedHandler"/>
    </http>
    <http pattern="/proxy.html" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/ieproxy.html" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/systime" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/metrics" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/users/authenticate" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/users/forgot-password-send-email" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/payment-gateway-webhooks/cashfree" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/payment-gateway-webhooks/cashfree/v2/payment" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/payment-gateway-webhooks/cashfree/v2023-08-01/payment" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/payment-gateway-webhooks/atom" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/payment-gateway-webhooks/jodo" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/payment-gateway-webhooks/razorpay" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/camsunit-device" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/mantra-device" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>
    <http pattern="/2.0/mantra-device/hello" auto-config="true" security="none"
          xmlns="http://www.springframework.org/schema/security">
    </http>

    <http pattern="${oauth.secure-pattern}" create-session="never"
          entry-point-ref="oauthAuthenticationEntryPoint" xmlns="http://www.springframework.org/schema/security">
        <intercept-url pattern="/**" method="OPTIONS"
                       access="IS_AUTHENTICATED_ANONYMOUSLY"/>
        <intercept-url pattern="/**"
                       access="ROLE_USER, ROLE_ADMIN, ROLE_INSTITUTE, ROLE_DASHBOARD, ROLE_USER_PII"/>
        <custom-filter ref="securityHeaderFilter" before="FIRST"/>
        <custom-filter ref="resourceServerFilter" before="PRE_AUTH_FILTER"/>
        <custom-filter ref="readOnlyScopeFilter" after="PRE_AUTH_FILTER" />
        <!--        <custom-filter ref="UUIDFilter" before="LAST" />-->
        <!--        <custom-filter ref="PIIFilter" after="PRE_AUTH_FILTER" />-->
        <access-denied-handler ref="oauthAccessDeniedHandler"/>
    </http>
</beans>