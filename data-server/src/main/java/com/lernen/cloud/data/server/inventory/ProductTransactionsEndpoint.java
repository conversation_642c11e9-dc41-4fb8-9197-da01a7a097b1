package com.lernen.cloud.data.server.inventory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

import com.embrate.cloud.pdf.inventory.store.StoreInventoryPDFInvoiceHandler;
import com.lernen.cloud.core.api.documents.DocumentOutput;
import com.lernen.cloud.core.api.exception.ApplicationErrorCode;
import com.lernen.cloud.core.api.exception.ApplicationException;
import com.lernen.cloud.core.api.exception.ErrorResponse;
import com.lernen.cloud.core.api.inventory.*;
import com.lernen.cloud.core.lib.inventory.ProductTransactionsManager;

/**
 * 
 * <AUTHOR>
 *
 */
@Path("/2.0/inventory/transactions")
public class ProductTransactionsEndpoint {

	private final ProductTransactionsManager productTransactionsManager;
	private final StoreInventoryPDFInvoiceHandler storeInventoryPDFInvoiceHandler;

	public ProductTransactionsEndpoint(ProductTransactionsManager productTransactionsManager, StoreInventoryPDFInvoiceHandler storeInventoryPDFInvoiceHandler) {
		this.productTransactionsManager = productTransactionsManager;
		this.storeInventoryPDFInvoiceHandler = storeInventoryPDFInvoiceHandler;
	}

	@GET
	@Path("test")
	@Produces(MediaType.APPLICATION_OCTET_STREAM)
	public Response test() throws IOException {
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		String content = "this is the content. working fine";
		bos.write(content.getBytes());
		return Response.status(Response.Status.OK.getStatusCode())
				.header("Content-Disposition", "attachment;filename=test.txt")
				.entity(new ByteArrayInputStream(bos.toByteArray())).build();
	}

	@GET
	@Path("{institute_id}/{transaction_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTransaction(@PathParam("institute_id") int instituteId,
			@PathParam("transaction_id") String transactionId) {
		TransactionSummary transactionSummary = productTransactionsManager.getTransactionDetails(instituteId,
				UUID.fromString(transactionId));
		if (transactionSummary == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No transaction for this id"));
		}
		transactionSummary.format();
		return Response.status(Response.Status.OK.getStatusCode()).entity(transactionSummary).build();
	}

	@GET
	@Path("{transaction_id}/pdf-invoice")
	@Produces("application/pdf")
	public Response getTransactionPDFInvoice(  @PathParam("transaction_id") UUID transactionId, @QueryParam("institute_id") int instituteId,
								   @QueryParam("store_copy") boolean storeCopy, @QueryParam("user_id") UUID userId) {
		DocumentOutput documentOutput = storeInventoryPDFInvoiceHandler.generateInvoice(instituteId, transactionId, storeCopy, userId);

		if(documentOutput == null){
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "No transaction found"));
		}

		return Response.status(Response.Status.OK.getStatusCode()).type("application/pdf")
				.header("Content-Disposition", "filename=" + documentOutput.getName())
				.entity(new ByteArrayInputStream(documentOutput.getContent().toByteArray())).build();
	}

	@GET
	@Path("/meta-data/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTransactionsMetaData(@PathParam("institute_id") int instituteId) {
		List<TransactionMetaData> transactionMetaDataList = productTransactionsManager
				.getTransactionsMetaData(instituteId);
		if (transactionMetaDataList == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Error occured while fetching transactions "));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(transactionMetaDataList).build();
	}

	@GET
	@Path("stats/{institute_id}")
	@Produces(MediaType.APPLICATION_JSON)
	public Response getTransactionStatistics(@PathParam("institute_id") int instituteId,
			@QueryParam("start") Integer start, @QueryParam("end") Integer end) {
		if (instituteId <= 0 || start == null || end == null || start < 0 || end < 0 || end < start) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Invalid parameters provided "));
		}
		TransactionsStatistics transactionsStatistics = productTransactionsManager
				.getTransactionsStatsDayWise(instituteId, start, end);
		if (transactionsStatistics == null) {
			throw new ApplicationException(new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST,
					"Error occured while fetching transactions stats "));
		}
		return Response.status(Response.Status.OK.getStatusCode()).entity(transactionsStatistics).build();
	}

	@POST
	@Path("purchase")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addPurchaseTransaction(PurchaseOrder purchaseOrder) {
		UUID purchaseTransactionId = productTransactionsManager.addPurchaseTransaction(purchaseOrder);
		if (purchaseTransactionId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_PURCHASE_ORDER, "Error occurred while adding purchase order"));
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("purchaseTransactionId", purchaseTransactionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(data).build();
	}

	@POST
	@Path("sale")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response addSalesTransaction(SalesOrderPayload salesOrderPayload) {
		UUID salesTransactionId = productTransactionsManager.addSalesTransaction(salesOrderPayload);
		if (salesTransactionId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Error occurred while adding sales order"));
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("salesTransactionId", salesTransactionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(data).build();
	}

	@POST
	@Path("return-sales-order")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response returnSalesTransaction(@QueryParam("institute_id") int instituteId, ReturnSalesOrder returnSalesOrder) {
		UUID salesReturnTransactionId = productTransactionsManager.returnSalesTransaction(instituteId, returnSalesOrder);
		if (salesReturnTransactionId == null) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_SALES_ORDER, "Error occurred while adding sales order"));
		}
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("salesReturnTransactionId", salesReturnTransactionId);
		return Response.status(Response.Status.OK.getStatusCode()).entity(data).build();
	}

	@DELETE
	@Path("{transaction_id}")
	@Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public Response deleteTransaction(@PathParam("transaction_id") UUID transactionId, @QueryParam("institute_id") int instituteId) {
		boolean success = productTransactionsManager.deleteTransaction(instituteId, transactionId);
		if (!success) {
			throw new ApplicationException(
					new ErrorResponse(ApplicationErrorCode.INVALID_REQUEST, "Error occurred while deleting transaction"));
		}
		return Response.status(Response.Status.OK.getStatusCode()).build();
	}
}
