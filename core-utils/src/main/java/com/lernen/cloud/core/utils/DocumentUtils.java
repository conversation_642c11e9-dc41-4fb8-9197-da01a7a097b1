package com.lernen.cloud.core.utils;

import com.embrate.cloud.core.api.institute.InstituteDocumentType;
import com.lernen.cloud.core.api.institute.Institute;
import com.lernen.cloud.core.api.institute.Standard;
import com.lernen.cloud.core.api.institute.StandardSessionDataPayload;
import com.lernen.cloud.core.api.institute.StandardSessionDocumentType;
import com.lernen.cloud.core.api.transport.Vehicle;
import com.lernen.cloud.core.api.transport.VehicleDocumentType;
import com.lernen.cloud.core.api.visitor.VisitorDetails;
import com.lernen.cloud.core.api.visitor.VisitorDocumentType;
import com.lernen.cloud.core.api.library.BookDetails;
import com.lernen.cloud.core.api.library.BookDetailsPayload;
import com.lernen.cloud.core.api.library.BookDocumentType;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.util.CollectionUtils;

import com.embrate.cloud.core.api.homework.HomeworkDetails;
import com.embrate.cloud.core.api.homework.HomeworkDocumentType;
import com.embrate.cloud.core.api.homework.HomeworkSubmissionDetails;
import com.lernen.cloud.core.api.common.FileData;
import com.lernen.cloud.core.api.student.Student;
import com.lernen.cloud.core.api.student.StudentDocumentType;
import com.lernen.cloud.core.api.user.Document;

/**
 * 
 * <AUTHOR>
 *
 */
public class DocumentUtils {

	public static final int ONE_KB = 1024;
	public static final int FILE_SIZE_LIMIT = 250 * 100;

	public static final int LOGO_FILE_SIZE_LIMIT = 50 * 100;

	public static final int FILE_COUNT_LIMIT = 15;
	public static final String JPEG = "jpeg";
	public static final String JPG = "jpg";
	public static final String PNG = "png";
	public static final String TXT = "txt";
	public static final String DOC = "doc";
	public static final String PDF = "pdf";
	public static final String S3_FILE_PATH_DELIMITER = "/";

	private static final Logger logger = LogManager.getLogger(DocumentUtils.class);

	public static boolean validStudentDocument(Student student, StudentDocumentType studentDocumentType,
			String documentName, FileData document) {
		if (student == null || studentDocumentType == null || document == null) {
			logger.error("student {} or studentDocumentType {} or document {} is empty", student, studentDocumentType, document);
			return false;
		}
		if (studentDocumentType == StudentDocumentType.OTHER && StringUtils.isBlank(documentName)) {
			logger.error("documentName {} empty", documentName);
			return false;
		}
		if (CollectionUtils.isEmpty(student.getStudentDocuments())) {
			logger.info("student.getStudentDocuments() {} empty", student.getStudentDocuments());
			return true;
		}
		final String fileExtension = FilenameUtils.getExtension(document.getFileName());
		if (studentDocumentType == StudentDocumentType.STUDENT_PROFILE_IMAGE && !validImageExtension(fileExtension)) {
			logger.error("Image extension {} not in valid format ", fileExtension);
			return false;
		}
		for (final Document<StudentDocumentType> studentDocument : student.getStudentDocuments()) {
			if (studentDocument.getDocumentType() == studentDocumentType
					&& studentDocumentType != StudentDocumentType.OTHER
					&& !studentDocument.isDocumentNotUploaded()) {
				return false;
			}
			if (studentDocument.getDocumentType() == studentDocumentType
					&& studentDocumentType == StudentDocumentType.OTHER
					&& documentName.equalsIgnoreCase(studentDocument.getDocumentName())) {
				return false;
			}
		}
		return true;
	}

	public static boolean validVehicleDocument(VehicleDocumentType vehicleDocumentType,
											   String documentName, FileData document) {
		if (vehicleDocumentType == null || document == null) {
			logger.error("vehicleDocumentType {} or document {} is empty", vehicleDocumentType, document);
			return false;
		}
		if (StringUtils.isBlank(documentName)) {
			logger.error("documentName {} empty", documentName);
			return false;
		}
		final String fileExtension = FilenameUtils.getExtension(document.getFileName());
		if (!validDocumentExtension(fileExtension)) {
			logger.error("Document extension {} not in valid format ", fileExtension);
			return false;
		}
		return true;
	}

	public static boolean validBookDocument(BookDetails bookDetails, BookDocumentType bookDocumentType,
			String documentName, FileData document) {
		if (bookDetails == null || bookDocumentType == null || document == null) {
			logger.error("Book {} or bookDocumentType {} or document {} is empty", bookDetails, bookDocumentType, document);
			return false;
		}
		if (bookDocumentType == BookDocumentType.OTHER && StringUtils.isBlank(documentName)) {
			logger.error("documentName {} empty", documentName);
			return false;
		}
		if (CollectionUtils.isEmpty(bookDetails.getBookDocument())) {
			logger.info(" bookDetails.getBookDocument() {} empty", bookDetails.getBookDocument());
			return true;
		}
		final String fileExtension = FilenameUtils.getExtension(document.getFileName());
		if (bookDocumentType == BookDocumentType.BOOK_COVER_IMAGE && !validImageExtension(fileExtension)) {
			logger.error("Image extension {} not in valid format ", fileExtension);
			return false;
		}
		for (final Document<BookDocumentType> BookDocument : bookDetails.getBookDocument()) {
			if (BookDocument.getDocumentType() == bookDocumentType
					&& bookDocumentType != BookDocumentType.OTHER
					&& !BookDocument.isDocumentNotUploaded()) {
				return false;
			}
			if (BookDocument.getDocumentType() == bookDocumentType
					&& bookDocumentType == BookDocumentType.OTHER
					&& documentName.equalsIgnoreCase(BookDocument.getDocumentName())) {
				return false;
			}
		}
		return true;
	}

	public static boolean validInstituteDocument(Institute institute, InstituteDocumentType instituteDocumentType,
												 String documentName, FileData document){
		if(institute == null || instituteDocumentType == null || document == null) {
			return false;
		}
		if(CollectionUtils.isEmpty(institute.getInstituteDocuments())) {
			return true;
		}
		final String fileExtension = FilenameUtils.getExtension(document.getFileName());
		if (!validImageExtension(fileExtension)) {
			logger.error("Image extension {} not in valid format ", fileExtension);
			return false;
		}
		if(StringUtils.isBlank(documentName)){
			return false;
		}
		for (final Document<InstituteDocumentType> instituteDocument : institute.getInstituteDocuments()) {
			if (instituteDocument.getDocumentType() == instituteDocumentType) {
				return false;
			}
		}

		return true;
	}

	public static boolean validVisitorDocument(VisitorDetails visitorDetails, VisitorDocumentType visitorDocumentType,
											   String documentName, FileData document) {
		if (visitorDetails == null || visitorDocumentType == null || document == null) {
			logger.error("visitor {} or visitorDocumentType {} or document {} is empty", visitorDetails, visitorDocumentType, document);
			return false;
		}
		if (visitorDocumentType == VisitorDocumentType.OTHER && StringUtils.isBlank(documentName)) {
			logger.error("documentName {} empty", documentName);
			return false;
		}
		if (CollectionUtils.isEmpty(visitorDetails.getVisitorDocuments())) {
			logger.info("visitorDetails.getVisitorDocuments() {} empty", visitorDetails.getVisitorDocuments());
			return true;
		}
		logger.info("document {}", document);
		final String fileExtension = FilenameUtils.getExtension(document.getFileName());
		logger.info("fileExtension {}", fileExtension);
		if (visitorDocumentType == VisitorDocumentType.VISITOR_PROFILE_IMAGE && !validImageExtension(fileExtension)) {
			logger.error("Image extension {} not in valid format ", fileExtension);
			return false;
		}
		for (final Document<VisitorDocumentType> visitorDocument : visitorDetails.getVisitorDocuments()) {
			if (visitorDocument.getDocumentType() == visitorDocumentType
					&& visitorDocumentType != VisitorDocumentType.OTHER
					&& !visitorDocument.isDocumentNotUploaded()) {
				return false;
			}
			if (visitorDocument.getDocumentType() == visitorDocumentType
					&& visitorDocumentType == VisitorDocumentType.OTHER
					&& documentName.equalsIgnoreCase(visitorDocument.getDocumentName())) {
				return false;
			}
		}
		return true;
	}

	public static boolean validImageExtension(String fileExtension) {
		return fileExtension.equalsIgnoreCase(PNG) || fileExtension.equalsIgnoreCase(JPEG)
				|| fileExtension.equalsIgnoreCase(JPG);
	}
	
	public static boolean validHomeworkDocument(HomeworkDetails homeworkDetails, HomeworkDocumentType homeworkDocumentType,
			FileData document) {
		if (homeworkDetails == null || homeworkDocumentType == null || document == null) {
			return false;
		}
		if (CollectionUtils.isEmpty(homeworkDetails.getHomeworkAttachments())) {
			return true;
		}
		final String fileExtension = FilenameUtils.getExtension(document.getFileName());
		if (homeworkDocumentType == HomeworkDocumentType.HOMEWORK_ATTACHMENTS && !validDocumentExtension(fileExtension)) {
			logger.error("Document extension {} not in valid format ", fileExtension);
			return false;
		}
		if (homeworkDocumentType == HomeworkDocumentType.HOMEWORK_SUBMISSION_ATTACHMENTS && !validDocumentExtension(fileExtension)) {
			logger.error("Document extension {} not in valid format ", fileExtension);
			return false;
		}
		for (final Document<HomeworkDocumentType> homeworkDocument : homeworkDetails.getHomeworkAttachments()) {
			if (document.getFileName().equalsIgnoreCase(homeworkDocument.getDocumentName())) {
				return false;
			}
		}
		return true;
	}
	
	public static boolean validDocumentExtension(String fileExtension) {
		return fileExtension.equalsIgnoreCase(PNG) || fileExtension.equalsIgnoreCase(JPEG)
				|| fileExtension.equalsIgnoreCase(JPG) || fileExtension.equalsIgnoreCase(TXT)
				|| fileExtension.equalsIgnoreCase(DOC) || fileExtension.equalsIgnoreCase(PDF);
	}
	
	public static boolean validHomeworkSubmissionDocument(HomeworkSubmissionDetails homeworkSubmissionDetails, HomeworkDocumentType homeworkDocumentType,
			FileData document) {
		if (homeworkSubmissionDetails == null || homeworkDocumentType == null || document == null) {
			return false;
		}
		if (CollectionUtils.isEmpty(homeworkSubmissionDetails.getHomeworkSubmissionAttachments())) {
			return true;
		}
		final String fileExtension = FilenameUtils.getExtension(document.getFileName());
		if (homeworkDocumentType == HomeworkDocumentType.HOMEWORK_ATTACHMENTS && !validDocumentExtension(fileExtension)) {
			logger.error("Document extension {} not in valid format ", fileExtension);
			return false;
		}
		if (homeworkDocumentType == HomeworkDocumentType.HOMEWORK_SUBMISSION_ATTACHMENTS && !validDocumentExtension(fileExtension)) {
			logger.error("Document extension {} not in valid format ", fileExtension);
			return false;
		}
		for (final Document<HomeworkDocumentType> homeworkDocument : homeworkSubmissionDetails.getHomeworkSubmissionAttachments()) {
			if (document.getFileName().equalsIgnoreCase(homeworkDocument.getDocumentName())) {
				return false;
			}
		}
		return true;
	}

	public static boolean validStandardSessionDocument(StandardSessionDataPayload standardSessionDataPayload, StandardSessionDocumentType standardSessionDocumentType,
													   String documentName, FileData document){
		//This will only be the case when no entry are present in DB for this case. Which is a valid scenario for our case
		if(standardSessionDataPayload == null) {
			return true;
		}
		if(standardSessionDocumentType == null || document == null) {
			return false;
		}
		if(CollectionUtils.isEmpty(standardSessionDataPayload.getStandardSessionDocumentList())) {
			return true;
		}
		if (standardSessionDocumentType == StandardSessionDocumentType.OTHER && StringUtils.isBlank(documentName)) {
			logger.error("documentName {} empty", documentName);
			return false;
		}
		if(StringUtils.isBlank(documentName)){
			return false;
		}
		for (final Document<StandardSessionDocumentType> standardSessionDocument : standardSessionDataPayload.getStandardSessionDocumentList()) {
			if (standardSessionDocument.getDocumentType() == standardSessionDocumentType
					&& standardSessionDocumentType != StandardSessionDocumentType.OTHER
					&& !standardSessionDocument.isDocumentNotUploaded()) {
				return false;
			}
			if (standardSessionDocument.getDocumentType() == standardSessionDocumentType
					&& standardSessionDocumentType == StandardSessionDocumentType.OTHER
					&& documentName.equalsIgnoreCase(standardSessionDocument.getDocumentName())) {
				return false;
			}
		}
		return true;
	}
}
